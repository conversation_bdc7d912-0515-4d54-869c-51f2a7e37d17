import { NextRequest, NextResponse } from 'next/server';
import { OpenRouterService } from '@/lib/openrouter';
import { ArticleGenerationRequest } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body: ArticleGenerationRequest = await request.json();
    
    if (!body.title || !body.language) {
      return NextResponse.json(
        { success: false, error: 'Title and language are required' },
        { status: 400 }
      );
    }

    const openRouterService = new OpenRouterService();
    const result = await openRouterService.generateArticle(body);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Article generation error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
