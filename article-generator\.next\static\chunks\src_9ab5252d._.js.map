{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Yeni%20klas%C3%B6r%20%283%29/article-generator/src/components/ArticleGenerator.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Wand2, Loader2, Copy, Download, Calendar, Image as ImageIcon } from 'lucide-react';\nimport { PexelsImage } from '@/types';\n\nexport function ArticleGenerator() {\n  const [title, setTitle] = useState('');\n  const [language, setLanguage] = useState<'tr' | 'en'>('tr');\n  const [generatedArticle, setGeneratedArticle] = useState('');\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [isGeneratingTitle, setIsGeneratingTitle] = useState(false);\n  const [selectedImages, setSelectedImages] = useState<PexelsImage[]>([]);\n  const [showImageSearch, setShowImageSearch] = useState(false);\n  const [imageSearchQuery, setImageSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState<PexelsImage[]>([]);\n  const [isSearchingImages, setIsSearchingImages] = useState(false);\n  const [generationProgress, setGenerationProgress] = useState('');\n  const [generationStartTime, setGenerationStartTime] = useState<number | null>(null);\n  const [elapsedTime, setElapsedTime] = useState(0);\n\n  // Timer effect\n  useEffect(() => {\n    let interval: NodeJS.Timeout;\n    if (isGenerating && generationStartTime) {\n      interval = setInterval(() => {\n        setElapsedTime(Math.floor((Date.now() - generationStartTime) / 1000));\n      }, 1000);\n    }\n    return () => {\n      if (interval) clearInterval(interval);\n    };\n  }, [isGenerating, generationStartTime]);\n\n  const generateTitle = async () => {\n    setIsGeneratingTitle(true);\n    try {\n      const response = await fetch('/api/generate-title', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ language })\n      });\n      const data = await response.json();\n      setTitle(data.title);\n    } catch (error) {\n      console.error('Title generation failed:', error);\n    } finally {\n      setIsGeneratingTitle(false);\n    }\n  };\n\n  const generateArticle = async () => {\n    if (!title.trim()) {\n      alert('Lütfen bir başlık girin');\n      return;\n    }\n\n    setIsGenerating(true);\n    setGeneratedArticle(''); // Clear previous content\n    setGenerationProgress('Makale oluşturma isteği gönderiliyor...');\n    setGenerationStartTime(Date.now());\n    setElapsedTime(0);\n\n    try {\n      console.log('Starting article generation for:', title);\n\n      setGenerationProgress('AI modeli ile bağlantı kuruluyor...');\n\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes timeout\n\n      const response = await fetch('/api/generate-article', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ title: title.trim(), language }),\n        signal: controller.signal\n      });\n\n      clearTimeout(timeoutId);\n\n      console.log('Response status:', response.status);\n      setGenerationProgress('Yanıt alındı, işleniyor...');\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      console.log('Response data:', data);\n      setGenerationProgress('Makale içeriği hazırlanıyor...');\n\n      if (data.success) {\n        setGeneratedArticle(data.content);\n        // Auto-search for images based on title\n        searchImages(title);\n\n        // Save article to localStorage\n        const article = {\n          id: Date.now().toString(),\n          title: title.trim(),\n          content: data.content,\n          language,\n          createdAt: new Date(),\n          status: 'draft' as const\n        };\n\n        const savedArticles = localStorage.getItem('saved-articles');\n        const existingArticles = savedArticles ? JSON.parse(savedArticles) : [];\n        const updatedArticles = [...existingArticles, article];\n        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));\n\n        alert('Makale başarıyla oluşturuldu!');\n      } else {\n        const errorMessage = data.error || 'Bilinmeyen hata oluştu';\n        console.error('Article generation failed:', errorMessage);\n        alert('Makale oluşturulurken hata oluştu: ' + errorMessage);\n      }\n    } catch (error: any) {\n      console.error('Article generation failed:', error);\n      let errorMessage = 'Makale oluşturulurken hata oluştu';\n\n      if (error.message.includes('timeout')) {\n        errorMessage = 'İstek zaman aşımına uğradı. Lütfen tekrar deneyin.';\n      } else if (error.message.includes('network')) {\n        errorMessage = 'Ağ bağlantısı hatası. İnternet bağlantınızı kontrol edin.';\n      } else if (error.message.includes('401')) {\n        errorMessage = 'API anahtarı geçersiz. Lütfen ayarları kontrol edin.';\n      } else if (error.message.includes('402')) {\n        errorMessage = 'Yetersiz kredi. OpenRouter hesabınıza kredi ekleyin.';\n      } else if (error.message.includes('429')) {\n        errorMessage = 'Çok fazla istek. Lütfen biraz bekleyip tekrar deneyin.';\n      }\n\n      alert(errorMessage);\n    } finally {\n      setIsGenerating(false);\n      setGenerationProgress('');\n      setGenerationStartTime(null);\n    }\n  };\n\n  const searchImages = async (query: string) => {\n    setIsSearchingImages(true);\n    try {\n      const response = await fetch(`/api/search-images?query=${encodeURIComponent(query)}&perPage=12`);\n      const data = await response.json();\n      setSearchResults(data.images || []);\n      setShowImageSearch(true);\n    } catch (error) {\n      console.error('Image search failed:', error);\n    } finally {\n      setIsSearchingImages(false);\n    }\n  };\n\n  const toggleImageSelection = (image: PexelsImage) => {\n    setSelectedImages(prev => {\n      const isSelected = prev.some(img => img.id === image.id);\n      if (isSelected) {\n        return prev.filter(img => img.id !== image.id);\n      } else {\n        return [...prev, image];\n      }\n    });\n  };\n\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(generatedArticle);\n    alert('Makale panoya kopyalandı!');\n  };\n\n  const downloadAsHTML = () => {\n    const blob = new Blob([generatedArticle], { type: 'text/html' });\n    const url = URL.createObjectURL(blob);\n    const a = document.createElement('a');\n    a.href = url;\n    a.download = `${title.replace(/[^a-zA-Z0-9]/g, '_')}.html`;\n    document.body.appendChild(a);\n    a.click();\n    document.body.removeChild(a);\n    URL.revokeObjectURL(url);\n  };\n\n  return (\n    <div className=\"max-w-6xl mx-auto\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Title Input Section */}\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Makale Başlığı\n              </label>\n              <div className=\"flex gap-2\">\n                <input\n                  type=\"text\"\n                  value={title}\n                  onChange={(e) => setTitle(e.target.value)}\n                  placeholder=\"Makale başlığını girin...\"\n                  className=\"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                />\n                <button\n                  onClick={generateTitle}\n                  disabled={isGeneratingTitle}\n                  className=\"px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 flex items-center gap-2\"\n                >\n                  {isGeneratingTitle ? (\n                    <Loader2 className=\"w-4 h-4 animate-spin\" />\n                  ) : (\n                    <Wand2 className=\"w-4 h-4\" />\n                  )}\n                  AI Başlık\n                </button>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Dil\n              </label>\n              <select\n                value={language}\n                onChange={(e) => setLanguage(e.target.value as 'tr' | 'en')}\n                className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"tr\">Türkçe</option>\n                <option value=\"en\">English</option>\n              </select>\n            </div>\n\n            {!isGenerating && (\n              <div className=\"mb-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\n                <p className=\"text-sm text-yellow-700 dark:text-yellow-300\">\n                  ⚠️ Makale oluşturma işlemi 3-5 dakika sürebilir. Lütfen sayfayı kapatmayın.\n                </p>\n              </div>\n            )}\n\n            <button\n              onClick={generateArticle}\n              disabled={isGenerating || !title.trim()}\n              className=\"w-full px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 flex items-center justify-center gap-2 font-medium\"\n            >\n              {isGenerating ? (\n                <>\n                  <Loader2 className=\"w-5 h-5 animate-spin\" />\n                  <div className=\"flex flex-col items-center\">\n                    <span>{generationProgress || 'Makale Oluşturuluyor...'}</span>\n                    <span className=\"text-sm opacity-75\">\n                      {Math.floor(elapsedTime / 60)}:{(elapsedTime % 60).toString().padStart(2, '0')}\n                    </span>\n                  </div>\n                </>\n              ) : (\n                <>\n                  <Wand2 className=\"w-5 h-5\" />\n                  Makale Oluştur\n                </>\n              )}\n            </button>\n          </div>\n\n          {/* Image Selection Section */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center justify-between\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                Görseller ({selectedImages.length} seçili)\n              </label>\n              <button\n                onClick={() => setShowImageSearch(!showImageSearch)}\n                className=\"px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center gap-1\"\n              >\n                <ImageIcon className=\"w-4 h-4\" />\n                Görsel Ara\n              </button>\n            </div>\n\n            {showImageSearch && (\n              <div className=\"space-y-3\">\n                <div className=\"flex gap-2\">\n                  <input\n                    type=\"text\"\n                    value={imageSearchQuery}\n                    onChange={(e) => setImageSearchQuery(e.target.value)}\n                    placeholder=\"Görsel arama...\"\n                    className=\"flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n                  />\n                  <button\n                    onClick={() => searchImages(imageSearchQuery)}\n                    disabled={isSearchingImages}\n                    className=\"px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50\"\n                  >\n                    {isSearchingImages ? (\n                      <Loader2 className=\"w-4 h-4 animate-spin\" />\n                    ) : (\n                      'Ara'\n                    )}\n                  </button>\n                </div>\n\n                <div className=\"grid grid-cols-3 gap-2 max-h-64 overflow-y-auto\">\n                  {searchResults.map((image) => (\n                    <div\n                      key={image.id}\n                      className={`relative cursor-pointer rounded-md overflow-hidden border-2 ${\n                        selectedImages.some(img => img.id === image.id)\n                          ? 'border-blue-500'\n                          : 'border-transparent'\n                      }`}\n                      onClick={() => toggleImageSelection(image)}\n                    >\n                      <img\n                        src={image.src.small}\n                        alt={`Photo by ${image.photographer}`}\n                        className=\"w-full h-20 object-cover\"\n                      />\n                      {selectedImages.some(img => img.id === image.id) && (\n                        <div className=\"absolute inset-0 bg-blue-500 bg-opacity-30 flex items-center justify-center\">\n                          <div className=\"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\">\n                            <span className=\"text-white text-xs\">✓</span>\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {selectedImages.length > 0 && (\n              <div className=\"space-y-2\">\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">Seçili Görseller:</p>\n                <div className=\"grid grid-cols-4 gap-2\">\n                  {selectedImages.map((image) => (\n                    <div key={image.id} className=\"relative\">\n                      <img\n                        src={image.src.small}\n                        alt={`Photo by ${image.photographer}`}\n                        className=\"w-full h-16 object-cover rounded-md\"\n                      />\n                      <button\n                        onClick={() => toggleImageSelection(image)}\n                        className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs hover:bg-red-600\"\n                      >\n                        ×\n                      </button>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Generated Article Display */}\n      {generatedArticle && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n              Oluşturulan Makale\n            </h2>\n            <div className=\"flex gap-2\">\n              <button\n                onClick={copyToClipboard}\n                className=\"px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center gap-2\"\n              >\n                <Copy className=\"w-4 h-4\" />\n                Kopyala\n              </button>\n              <button\n                onClick={downloadAsHTML}\n                className=\"px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center gap-2\"\n              >\n                <Download className=\"w-4 h-4\" />\n                İndir\n              </button>\n            </div>\n          </div>\n          \n          <div className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto\">\n            <div \n              className=\"prose dark:prose-invert max-w-none\"\n              dangerouslySetInnerHTML={{ __html: generatedArticle }}\n            />\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAMO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI;YACJ,IAAI,gBAAgB,qBAAqB;gBACvC,WAAW;kDAAY;wBACrB,eAAe,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,mBAAmB,IAAI;oBACjE;iDAAG;YACL;YACA;8CAAO;oBACL,IAAI,UAAU,cAAc;gBAC9B;;QACF;qCAAG;QAAC;QAAc;KAAoB;IAEtC,MAAM,gBAAgB;QACpB,qBAAqB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,KAAK,KAAK;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,MAAM;YACN;QACF;QAEA,gBAAgB;QAChB,oBAAoB,KAAK,yBAAyB;QAClD,sBAAsB;QACtB,uBAAuB,KAAK,GAAG;QAC/B,eAAe;QAEf,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,sBAAsB;YAEtB,MAAM,aAAa,IAAI;YACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,SAAS,oBAAoB;YAEpF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,OAAO,MAAM,IAAI;oBAAI;gBAAS;gBACrD,QAAQ,WAAW,MAAM;YAC3B;YAEA,aAAa;YAEb,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;YAC/C,sBAAsB;YAEtB,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,sBAAsB;YAEtB,IAAI,KAAK,OAAO,EAAE;gBAChB,oBAAoB,KAAK,OAAO;gBAChC,wCAAwC;gBACxC,aAAa;gBAEb,+BAA+B;gBAC/B,MAAM,UAAU;oBACd,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACvB,OAAO,MAAM,IAAI;oBACjB,SAAS,KAAK,OAAO;oBACrB;oBACA,WAAW,IAAI;oBACf,QAAQ;gBACV;gBAEA,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,MAAM,mBAAmB,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,EAAE;gBACvE,MAAM,kBAAkB;uBAAI;oBAAkB;iBAAQ;gBACtD,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;gBAEtD,MAAM;YACR,OAAO;gBACL,MAAM,eAAe,KAAK,KAAK,IAAI;gBACnC,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,MAAM,wCAAwC;YAChD;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,IAAI,eAAe;YAEnB,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;gBACrC,eAAe;YACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY;gBAC5C,eAAe;YACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;gBACxC,eAAe;YACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;gBACxC,eAAe;YACjB,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ;gBACxC,eAAe;YACjB;YAEA,MAAM;QACR,SAAU;YACR,gBAAgB;YAChB,sBAAsB;YACtB,uBAAuB;QACzB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,qBAAqB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,yBAAyB,EAAE,mBAAmB,OAAO,WAAW,CAAC;YAC/F,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,iBAAiB,KAAK,MAAM,IAAI,EAAE;YAClC,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB,CAAA;YAChB,MAAM,aAAa,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,MAAM,EAAE;YACvD,IAAI,YAAY;gBACd,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,MAAM,EAAE;YAC/C,OAAO;gBACL,OAAO;uBAAI;oBAAM;iBAAM;YACzB;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,MAAM;IACR;IAEA,MAAM,iBAAiB;QACrB,MAAM,OAAO,IAAI,KAAK;YAAC;SAAiB,EAAE;YAAE,MAAM;QAAY;QAC9D,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,GAAG,MAAM,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC;QAC1D,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDACC,SAAS;oDACT,UAAU;oDACV,WAAU;;wDAET,kCACC,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;iFAEnB,6LAAC,kNAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDACjB;;;;;;;;;;;;;;;;;;;8CAMR,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAAkE;;;;;;sDAGnF,6LAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAK;;;;;;8DACnB,6LAAC;oDAAO,OAAM;8DAAK;;;;;;;;;;;;;;;;;;gCAItB,CAAC,8BACA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAA+C;;;;;;;;;;;8CAMhE,6LAAC;oCACC,SAAS;oCACT,UAAU,gBAAgB,CAAC,MAAM,IAAI;oCACrC,WAAU;8CAET,6BACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;0DACnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAM,sBAAsB;;;;;;kEAC7B,6LAAC;wDAAK,WAAU;;4DACb,KAAK,KAAK,CAAC,cAAc;4DAAI;4DAAE,CAAC,cAAc,EAAE,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;qEAKhF;;0DACE,6LAAC,kNAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;sCAQrC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;;gDAA6D;gDAChE,eAAe,MAAM;gDAAC;;;;;;;sDAEpC,6LAAC;4CACC,SAAS,IAAM,mBAAmB,CAAC;4CACnC,WAAU;;8DAEV,6LAAC,uMAAA,CAAA,QAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;gCAKpC,iCACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;oDACnD,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,UAAU;oDACV,WAAU;8DAET,kCACC,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;+DAEnB;;;;;;;;;;;;sDAKN,6LAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC;oDAEC,WAAW,CAAC,4DAA4D,EACtE,eAAe,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,MAAM,EAAE,IAC1C,oBACA,sBACJ;oDACF,SAAS,IAAM,qBAAqB;;sEAEpC,6LAAC;4DACC,KAAK,MAAM,GAAG,CAAC,KAAK;4DACpB,KAAK,CAAC,SAAS,EAAE,MAAM,YAAY,EAAE;4DACrC,WAAU;;;;;;wDAEX,eAAe,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,MAAM,EAAE,mBAC7C,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;;;;;;;mDAhBtC,MAAM,EAAE;;;;;;;;;;;;;;;;gCA0BtB,eAAe,MAAM,GAAG,mBACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;sDACxD,6LAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;oDAAmB,WAAU;;sEAC5B,6LAAC;4DACC,KAAK,MAAM,GAAG,CAAC,KAAK;4DACpB,KAAK,CAAC,SAAS,EAAE,MAAM,YAAY,EAAE;4DACrC,WAAU;;;;;;sEAEZ,6LAAC;4DACC,SAAS,IAAM,qBAAqB;4DACpC,WAAU;sEACX;;;;;;;mDATO,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAsB/B,kCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAG9B,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;kCAMtC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,WAAU;4BACV,yBAAyB;gCAAE,QAAQ;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAOlE;GAhYgB;KAAA", "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Yeni%20klas%C3%B6r%20%283%29/article-generator/src/components/WordPressConfig.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { CheckCircle, XCircle, Loader2, Save, Send, Eye } from 'lucide-react';\nimport { WordPressConfig as WPConfig, Article } from '@/types';\n\nexport function WordPressConfig() {\n  const [config, setConfig] = useState<WPConfig>({\n    siteUrl: '',\n    username: '',\n    appPassword: ''\n  });\n  const [isConnected, setIsConnected] = useState<boolean | null>(null);\n  const [isTesting, setIsTesting] = useState(false);\n  const [savedArticles, setSavedArticles] = useState<Article[]>([]);\n  const [isPublishing, setIsPublishing] = useState<string | null>(null);\n\n  useEffect(() => {\n    // Load saved config from localStorage\n    const savedConfig = localStorage.getItem('wordpress-config');\n    if (savedConfig) {\n      setConfig(JSON.parse(savedConfig));\n    }\n\n    // Load saved articles from localStorage\n    const savedArticlesData = localStorage.getItem('saved-articles');\n    if (savedArticlesData) {\n      setSavedArticles(JSON.parse(savedArticlesData));\n    }\n  }, []);\n\n  const saveConfig = () => {\n    localStorage.setItem('wordpress-config', JSON.stringify(config));\n    alert('WordPress ayarları kaydedildi!');\n  };\n\n  const testConnection = async () => {\n    if (!config.siteUrl || !config.username || !config.appPassword) {\n      alert('Lütfen tüm alanları doldurun');\n      return;\n    }\n\n    setIsTesting(true);\n    try {\n      const response = await fetch('/api/wordpress', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'test',\n          config\n        })\n      });\n      const data = await response.json();\n      setIsConnected(data.success);\n      \n      if (data.success) {\n        alert('WordPress bağlantısı başarılı!');\n        saveConfig();\n      } else {\n        alert('WordPress bağlantısı başarısız. Lütfen bilgilerinizi kontrol edin.');\n      }\n    } catch (error) {\n      console.error('Connection test failed:', error);\n      setIsConnected(false);\n      alert('Bağlantı testi sırasında hata oluştu');\n    } finally {\n      setIsTesting(false);\n    }\n  };\n\n  const publishToWordPress = async (article: Article) => {\n    if (!isConnected) {\n      alert('Önce WordPress bağlantısını test edin');\n      return;\n    }\n\n    setIsPublishing(article.id || '');\n    try {\n      const response = await fetch('/api/wordpress', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'create',\n          config,\n          article: {\n            ...article,\n            status: 'published'\n          }\n        })\n      });\n      const data = await response.json();\n      \n      if (data.success) {\n        alert('Makale WordPress\\'e başarıyla yayınlandı!');\n        // Update article with WordPress post ID\n        const updatedArticles = savedArticles.map(a => \n          a.id === article.id \n            ? { ...a, wordpressPostId: data.postId, status: 'published' as const }\n            : a\n        );\n        setSavedArticles(updatedArticles);\n        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));\n      } else {\n        alert('WordPress\\'e yayınlama başarısız');\n      }\n    } catch (error) {\n      console.error('Publishing failed:', error);\n      alert('Yayınlama sırasında hata oluştu');\n    } finally {\n      setIsPublishing(null);\n    }\n  };\n\n  const scheduleArticle = async (article: Article, scheduledDate: Date) => {\n    if (!isConnected) {\n      alert('Önce WordPress bağlantısını test edin');\n      return;\n    }\n\n    try {\n      const response = await fetch('/api/wordpress', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'create',\n          config,\n          article: {\n            ...article,\n            status: 'scheduled',\n            scheduledAt: scheduledDate\n          }\n        })\n      });\n      const data = await response.json();\n      \n      if (data.success) {\n        alert('Makale planlandı!');\n        const updatedArticles = savedArticles.map(a => \n          a.id === article.id \n            ? { ...a, wordpressPostId: data.postId, status: 'scheduled' as const, scheduledAt: scheduledDate }\n            : a\n        );\n        setSavedArticles(updatedArticles);\n        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));\n      } else {\n        alert('Makale planlama başarısız');\n      }\n    } catch (error) {\n      console.error('Scheduling failed:', error);\n      alert('Planlama sırasında hata oluştu');\n    }\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-6\">\n      {/* WordPress Configuration */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          WordPress Bağlantı Ayarları\n        </h2>\n        \n        <div className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              WordPress Site URL\n            </label>\n            <input\n              type=\"url\"\n              value={config.siteUrl}\n              onChange={(e) => setConfig(prev => ({ ...prev, siteUrl: e.target.value }))}\n              placeholder=\"https://yoursite.com\"\n              className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Kullanıcı Adı\n            </label>\n            <input\n              type=\"text\"\n              value={config.username}\n              onChange={(e) => setConfig(prev => ({ ...prev, username: e.target.value }))}\n              placeholder=\"WordPress kullanıcı adınız\"\n              className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Uygulama Şifresi\n            </label>\n            <input\n              type=\"password\"\n              value={config.appPassword}\n              onChange={(e) => setConfig(prev => ({ ...prev, appPassword: e.target.value }))}\n              placeholder=\"WordPress uygulama şifreniz\"\n              className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n            />\n            <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n              WordPress Admin → Kullanıcılar → Profil → Uygulama Şifreleri bölümünden oluşturabilirsiniz\n            </p>\n          </div>\n\n          <div className=\"flex gap-3\">\n            <button\n              onClick={saveConfig}\n              className=\"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center gap-2\"\n            >\n              <Save className=\"w-4 h-4\" />\n              Kaydet\n            </button>\n            \n            <button\n              onClick={testConnection}\n              disabled={isTesting}\n              className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 flex items-center gap-2\"\n            >\n              {isTesting ? (\n                <Loader2 className=\"w-4 h-4 animate-spin\" />\n              ) : isConnected === true ? (\n                <CheckCircle className=\"w-4 h-4\" />\n              ) : isConnected === false ? (\n                <XCircle className=\"w-4 h-4\" />\n              ) : (\n                <Eye className=\"w-4 h-4\" />\n              )}\n              Bağlantıyı Test Et\n            </button>\n          </div>\n\n          {isConnected !== null && (\n            <div className={`p-3 rounded-lg ${\n              isConnected \n                ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' \n                : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'\n            }`}>\n              {isConnected \n                ? '✅ WordPress bağlantısı başarılı!' \n                : '❌ WordPress bağlantısı başarısız. Lütfen bilgilerinizi kontrol edin.'\n              }\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Saved Articles */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n          Kaydedilmiş Makaleler\n        </h2>\n        \n        {savedArticles.length === 0 ? (\n          <p className=\"text-gray-500 dark:text-gray-400 text-center py-8\">\n            Henüz kaydedilmiş makale bulunmuyor\n          </p>\n        ) : (\n          <div className=\"space-y-4\">\n            {savedArticles.map((article) => (\n              <div key={article.id} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-medium text-gray-900 dark:text-white mb-1\">\n                      {article.title}\n                    </h3>\n                    <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-2\">\n                      {article.language === 'tr' ? 'Türkçe' : 'English'} • \n                      {new Date(article.createdAt).toLocaleDateString('tr-TR')}\n                    </p>\n                    <div className=\"flex items-center gap-2\">\n                      <span className={`px-2 py-1 text-xs rounded-full ${\n                        article.status === 'published' \n                          ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300'\n                          : article.status === 'scheduled'\n                          ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300'\n                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'\n                      }`}>\n                        {article.status === 'published' ? 'Yayınlandı' : \n                         article.status === 'scheduled' ? 'Planlandı' : 'Taslak'}\n                      </span>\n                      {article.wordpressPostId && (\n                        <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                          WP ID: {article.wordpressPostId}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex gap-2\">\n                    {article.status === 'draft' && (\n                      <button\n                        onClick={() => publishToWordPress(article)}\n                        disabled={isPublishing === article.id || !isConnected}\n                        className=\"px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 flex items-center gap-1 text-sm\"\n                      >\n                        {isPublishing === article.id ? (\n                          <Loader2 className=\"w-3 h-3 animate-spin\" />\n                        ) : (\n                          <Send className=\"w-3 h-3\" />\n                        )}\n                        Yayınla\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAMO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QAC7C,SAAS;QACT,UAAU;QACV,aAAa;IACf;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,sCAAsC;YACtC,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,aAAa;gBACf,UAAU,KAAK,KAAK,CAAC;YACvB;YAEA,wCAAwC;YACxC,MAAM,oBAAoB,aAAa,OAAO,CAAC;YAC/C,IAAI,mBAAmB;gBACrB,iBAAiB,KAAK,KAAK,CAAC;YAC9B;QACF;oCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;QACxD,MAAM;IACR;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,OAAO,OAAO,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC,OAAO,WAAW,EAAE;YAC9D,MAAM;YACN;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR;gBACF;YACF;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,eAAe,KAAK,OAAO;YAE3B,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN;YACF,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,eAAe;YACf,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,aAAa;YAChB,MAAM;YACN;QACF;QAEA,gBAAgB,QAAQ,EAAE,IAAI;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR;oBACA,SAAS;wBACP,GAAG,OAAO;wBACV,QAAQ;oBACV;gBACF;YACF;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,wCAAwC;gBACxC,MAAM,kBAAkB,cAAc,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,QAAQ,EAAE,GACf;wBAAE,GAAG,CAAC;wBAAE,iBAAiB,KAAK,MAAM;wBAAE,QAAQ;oBAAqB,IACnE;gBAEN,iBAAiB;gBACjB,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YACxD,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,kBAAkB,OAAO,SAAkB;QAC/C,IAAI,CAAC,aAAa;YAChB,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR;oBACA,SAAS;wBACP,GAAG,OAAO;wBACV,QAAQ;wBACR,aAAa;oBACf;gBACF;YACF;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBACN,MAAM,kBAAkB,cAAc,GAAG,CAAC,CAAA,IACxC,EAAE,EAAE,KAAK,QAAQ,EAAE,GACf;wBAAE,GAAG,CAAC;wBAAE,iBAAiB,KAAK,MAAM;wBAAE,QAAQ;wBAAsB,aAAa;oBAAc,IAC/F;gBAEN,iBAAiB;gBACjB,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YACxD,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;kCAIzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO,OAAO,OAAO;wCACrB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACxE,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO,OAAO,QAAQ;wCACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACzE,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO,OAAO,WAAW;wCACzB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC5E,aAAY;wCACZ,WAAU;;;;;;kDAEZ,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;0CAK/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAI9B,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;4CAET,0BACC,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;uDACjB,gBAAgB,qBAClB,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;uDACrB,gBAAgB,sBAClB,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CACf;;;;;;;;;;;;;4BAKL,gBAAgB,sBACf,6LAAC;gCAAI,WAAW,CAAC,eAAe,EAC9B,cACI,wEACA,+DACJ;0CACC,cACG,qCACA;;;;;;;;;;;;;;;;;;0BAQZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;oBAIxE,cAAc,MAAM,KAAK,kBACxB,6LAAC;wBAAE,WAAU;kCAAoD;;;;;6CAIjE,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,wBAClB,6LAAC;gCAAqB,WAAU;0CAC9B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,6LAAC;oDAAE,WAAU;;wDACV,QAAQ,QAAQ,KAAK,OAAO,WAAW;wDAAU;wDACjD,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC;;;;;;;8DAElD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,+BAA+B,EAC/C,QAAQ,MAAM,KAAK,cACf,yEACA,QAAQ,MAAM,KAAK,cACnB,6EACA,iEACJ;sEACC,QAAQ,MAAM,KAAK,cAAc,eACjC,QAAQ,MAAM,KAAK,cAAc,cAAc;;;;;;wDAEjD,QAAQ,eAAe,kBACtB,6LAAC;4DAAK,WAAU;;gEAA2C;gEACjD,QAAQ,eAAe;;;;;;;;;;;;;;;;;;;sDAMvC,6LAAC;4CAAI,WAAU;sDACZ,QAAQ,MAAM,KAAK,yBAClB,6LAAC;gDACC,SAAS,IAAM,mBAAmB;gDAClC,UAAU,iBAAiB,QAAQ,EAAE,IAAI,CAAC;gDAC1C,WAAU;;oDAET,iBAAiB,QAAQ,EAAE,iBAC1B,6LAAC,oNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAChB;;;;;;;;;;;;;;;;;;+BAxCF,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAqDlC;GAlTgB;KAAA", "debugId": null}}, {"offset": {"line": 1226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Yeni%20klas%C3%B6r%20%283%29/article-generator/src/components/ScheduledArticles.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Calendar, Clock, Edit, Trash2, Send, Plus } from 'lucide-react';\nimport { Article } from '@/types';\nimport { format } from 'date-fns';\nimport { tr } from 'date-fns/locale';\n\nexport function ScheduledArticles() {\n  const [articles, setArticles] = useState<Article[]>([]);\n  const [showScheduleForm, setShowScheduleForm] = useState(false);\n  const [newSchedule, setNewSchedule] = useState({\n    title: '',\n    language: 'tr' as 'tr' | 'en',\n    scheduledDate: '',\n    scheduledTime: ''\n  });\n\n  useEffect(() => {\n    // Load saved articles from localStorage\n    const savedArticles = localStorage.getItem('saved-articles');\n    if (savedArticles) {\n      const parsedArticles = JSON.parse(savedArticles);\n      setArticles(parsedArticles.filter((article: Article) => \n        article.status === 'scheduled' || article.scheduledAt\n      ));\n    }\n  }, []);\n\n  const scheduleNewArticle = async () => {\n    if (!newSchedule.title || !newSchedule.scheduledDate || !newSchedule.scheduledTime) {\n      alert('Lütfen tüm alanları doldurun');\n      return;\n    }\n\n    const scheduledDateTime = new Date(`${newSchedule.scheduledDate}T${newSchedule.scheduledTime}`);\n    \n    if (scheduledDateTime <= new Date()) {\n      alert('Planlanan tarih gelecekte olmalıdır');\n      return;\n    }\n\n    // Generate article content\n    try {\n      const response = await fetch('/api/generate-article', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ \n          title: newSchedule.title, \n          language: newSchedule.language \n        })\n      });\n      const data = await response.json();\n      \n      if (data.success) {\n        const newArticle: Article = {\n          id: Date.now().toString(),\n          title: newSchedule.title,\n          content: data.content,\n          language: newSchedule.language,\n          createdAt: new Date(),\n          scheduledAt: scheduledDateTime,\n          status: 'scheduled'\n        };\n\n        // Save to localStorage\n        const savedArticles = localStorage.getItem('saved-articles');\n        const existingArticles = savedArticles ? JSON.parse(savedArticles) : [];\n        const updatedArticles = [...existingArticles, newArticle];\n        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));\n        \n        setArticles(prev => [...prev, newArticle]);\n        setShowScheduleForm(false);\n        setNewSchedule({\n          title: '',\n          language: 'tr',\n          scheduledDate: '',\n          scheduledTime: ''\n        });\n        \n        alert('Makale başarıyla planlandı!');\n      } else {\n        alert('Makale oluşturulurken hata oluştu: ' + data.error);\n      }\n    } catch (error) {\n      console.error('Article scheduling failed:', error);\n      alert('Makale planlama sırasında hata oluştu');\n    }\n  };\n\n  const publishNow = async (article: Article) => {\n    // Get WordPress config\n    const wpConfig = localStorage.getItem('wordpress-config');\n    if (!wpConfig) {\n      alert('Önce WordPress ayarlarını yapılandırın');\n      return;\n    }\n\n    try {\n      const response = await fetch('/api/wordpress', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: 'create',\n          config: JSON.parse(wpConfig),\n          article: {\n            ...article,\n            status: 'published'\n          }\n        })\n      });\n      const data = await response.json();\n      \n      if (data.success) {\n        alert('Makale başarıyla yayınlandı!');\n        \n        // Update article status\n        const updatedArticles = articles.map(a => \n          a.id === article.id \n            ? { ...a, status: 'published' as const, wordpressPostId: data.postId }\n            : a\n        );\n        setArticles(updatedArticles);\n        \n        // Update localStorage\n        const savedArticles = localStorage.getItem('saved-articles');\n        const allArticles = savedArticles ? JSON.parse(savedArticles) : [];\n        const updatedAllArticles = allArticles.map((a: Article) => \n          a.id === article.id \n            ? { ...a, status: 'published' as const, wordpressPostId: data.postId }\n            : a\n        );\n        localStorage.setItem('saved-articles', JSON.stringify(updatedAllArticles));\n      } else {\n        alert('Yayınlama başarısız');\n      }\n    } catch (error) {\n      console.error('Publishing failed:', error);\n      alert('Yayınlama sırasında hata oluştu');\n    }\n  };\n\n  const deleteScheduledArticle = (articleId: string) => {\n    if (confirm('Bu planlanmış makaleyi silmek istediğinizden emin misiniz?')) {\n      const updatedArticles = articles.filter(a => a.id !== articleId);\n      setArticles(updatedArticles);\n      \n      // Update localStorage\n      const savedArticles = localStorage.getItem('saved-articles');\n      const allArticles = savedArticles ? JSON.parse(savedArticles) : [];\n      const updatedAllArticles = allArticles.filter((a: Article) => a.id !== articleId);\n      localStorage.setItem('saved-articles', JSON.stringify(updatedAllArticles));\n    }\n  };\n\n  const updateSchedule = (articleId: string, newDate: Date) => {\n    const updatedArticles = articles.map(a => \n      a.id === articleId \n        ? { ...a, scheduledAt: newDate }\n        : a\n    );\n    setArticles(updatedArticles);\n    \n    // Update localStorage\n    const savedArticles = localStorage.getItem('saved-articles');\n    const allArticles = savedArticles ? JSON.parse(savedArticles) : [];\n    const updatedAllArticles = allArticles.map((a: Article) => \n      a.id === articleId \n        ? { ...a, scheduledAt: newDate }\n        : a\n    );\n    localStorage.setItem('saved-articles', JSON.stringify(updatedAllArticles));\n  };\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n              Planlı Makaleler\n            </h2>\n            <p className=\"text-gray-600 dark:text-gray-400 mt-1\">\n              Gelecekte yayınlanacak makalelerinizi yönetin\n            </p>\n          </div>\n          <button\n            onClick={() => setShowScheduleForm(true)}\n            className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center gap-2\"\n          >\n            <Plus className=\"w-4 h-4\" />\n            Yeni Planlı Makale\n          </button>\n        </div>\n      </div>\n\n      {/* Schedule Form */}\n      {showScheduleForm && (\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Yeni Makale Planla\n          </h3>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div className=\"md:col-span-2\">\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Makale Başlığı\n              </label>\n              <input\n                type=\"text\"\n                value={newSchedule.title}\n                onChange={(e) => setNewSchedule(prev => ({ ...prev, title: e.target.value }))}\n                placeholder=\"Makale başlığını girin...\"\n                className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Dil\n              </label>\n              <select\n                value={newSchedule.language}\n                onChange={(e) => setNewSchedule(prev => ({ ...prev, language: e.target.value as 'tr' | 'en' }))}\n                className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n              >\n                <option value=\"tr\">Türkçe</option>\n                <option value=\"en\">English</option>\n              </select>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Tarih\n              </label>\n              <input\n                type=\"date\"\n                value={newSchedule.scheduledDate}\n                onChange={(e) => setNewSchedule(prev => ({ ...prev, scheduledDate: e.target.value }))}\n                min={new Date().toISOString().split('T')[0]}\n                className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n              />\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Saat\n              </label>\n              <input\n                type=\"time\"\n                value={newSchedule.scheduledTime}\n                onChange={(e) => setNewSchedule(prev => ({ ...prev, scheduledTime: e.target.value }))}\n                className=\"w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white\"\n              />\n            </div>\n          </div>\n          \n          <div className=\"flex gap-3 mt-6\">\n            <button\n              onClick={scheduleNewArticle}\n              className=\"px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center gap-2\"\n            >\n              <Calendar className=\"w-4 h-4\" />\n              Planla\n            </button>\n            <button\n              onClick={() => setShowScheduleForm(false)}\n              className=\"px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500\"\n            >\n              İptal\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Scheduled Articles List */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6\">\n        {articles.length === 0 ? (\n          <div className=\"text-center py-12\">\n            <Calendar className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500 dark:text-gray-400\">\n              Henüz planlanmış makale bulunmuyor\n            </p>\n          </div>\n        ) : (\n          <div className=\"space-y-4\">\n            {articles.map((article) => (\n              <div key={article.id} className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4\">\n                <div className=\"flex items-start justify-between\">\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-medium text-gray-900 dark:text-white mb-2\">\n                      {article.title}\n                    </h3>\n                    \n                    <div className=\"flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-3\">\n                      <span className=\"flex items-center gap-1\">\n                        <Clock className=\"w-4 h-4\" />\n                        {article.scheduledAt && format(new Date(article.scheduledAt), 'dd MMMM yyyy, HH:mm', { locale: tr })}\n                      </span>\n                      <span>{article.language === 'tr' ? 'Türkçe' : 'English'}</span>\n                      <span className={`px-2 py-1 rounded-full text-xs ${\n                        article.status === 'published' \n                          ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300'\n                          : 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300'\n                      }`}>\n                        {article.status === 'published' ? 'Yayınlandı' : 'Planlandı'}\n                      </span>\n                    </div>\n                    \n                    <div className=\"text-sm text-gray-600 dark:text-gray-400\">\n                      {article.scheduledAt && new Date(article.scheduledAt) <= new Date() && article.status !== 'published' && (\n                        <span className=\"text-orange-600 dark:text-orange-400 font-medium\">\n                          ⚠️ Planlanan zaman geçti - Manuel yayınlama gerekli\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex gap-2\">\n                    {article.status !== 'published' && (\n                      <>\n                        <button\n                          onClick={() => publishNow(article)}\n                          className=\"px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center gap-1 text-sm\"\n                        >\n                          <Send className=\"w-3 h-3\" />\n                          Şimdi Yayınla\n                        </button>\n                        <button\n                          onClick={() => {\n                            const newDateTime = prompt('Yeni tarih ve saat (YYYY-MM-DD HH:MM):', \n                              article.scheduledAt ? format(new Date(article.scheduledAt), 'yyyy-MM-dd HH:mm') : ''\n                            );\n                            if (newDateTime) {\n                              const newDate = new Date(newDateTime);\n                              if (newDate > new Date()) {\n                                updateSchedule(article.id!, newDate);\n                              } else {\n                                alert('Yeni tarih gelecekte olmalıdır');\n                              }\n                            }\n                          }}\n                          className=\"px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center gap-1 text-sm\"\n                        >\n                          <Edit className=\"w-3 h-3\" />\n                          Düzenle\n                        </button>\n                      </>\n                    )}\n                    <button\n                      onClick={() => deleteScheduledArticle(article.id!)}\n                      className=\"px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 flex items-center gap-1 text-sm\"\n                    >\n                      <Trash2 className=\"w-3 h-3\" />\n                      Sil\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;;;AANA;;;;;AAQO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,OAAO;QACP,UAAU;QACV,eAAe;QACf,eAAe;IACjB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,wCAAwC;YACxC,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,eAAe;gBACjB,MAAM,iBAAiB,KAAK,KAAK,CAAC;gBAClC,YAAY,eAAe,MAAM;mDAAC,CAAC,UACjC,QAAQ,MAAM,KAAK,eAAe,QAAQ,WAAW;;YAEzD;QACF;sCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,aAAa,IAAI,CAAC,YAAY,aAAa,EAAE;YAClF,MAAM;YACN;QACF;QAEA,MAAM,oBAAoB,IAAI,KAAK,GAAG,YAAY,aAAa,CAAC,CAAC,EAAE,YAAY,aAAa,EAAE;QAE9F,IAAI,qBAAqB,IAAI,QAAQ;YACnC,MAAM;YACN;QACF;QAEA,2BAA2B;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,OAAO,YAAY,KAAK;oBACxB,UAAU,YAAY,QAAQ;gBAChC;YACF;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,aAAsB;oBAC1B,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACvB,OAAO,YAAY,KAAK;oBACxB,SAAS,KAAK,OAAO;oBACrB,UAAU,YAAY,QAAQ;oBAC9B,WAAW,IAAI;oBACf,aAAa;oBACb,QAAQ;gBACV;gBAEA,uBAAuB;gBACvB,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,MAAM,mBAAmB,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,EAAE;gBACvE,MAAM,kBAAkB;uBAAI;oBAAkB;iBAAW;gBACzD,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;gBAEtD,YAAY,CAAA,OAAQ;2BAAI;wBAAM;qBAAW;gBACzC,oBAAoB;gBACpB,eAAe;oBACb,OAAO;oBACP,UAAU;oBACV,eAAe;oBACf,eAAe;gBACjB;gBAEA,MAAM;YACR,OAAO;gBACL,MAAM,wCAAwC,KAAK,KAAK;YAC1D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,uBAAuB;QACvB,MAAM,WAAW,aAAa,OAAO,CAAC;QACtC,IAAI,CAAC,UAAU;YACb,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,kBAAkB;gBAC7C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,QAAQ,KAAK,KAAK,CAAC;oBACnB,SAAS;wBACP,GAAG,OAAO;wBACV,QAAQ;oBACV;gBACF;YACF;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM;gBAEN,wBAAwB;gBACxB,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAA,IACnC,EAAE,EAAE,KAAK,QAAQ,EAAE,GACf;wBAAE,GAAG,CAAC;wBAAE,QAAQ;wBAAsB,iBAAiB,KAAK,MAAM;oBAAC,IACnE;gBAEN,YAAY;gBAEZ,sBAAsB;gBACtB,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,MAAM,cAAc,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,EAAE;gBAClE,MAAM,qBAAqB,YAAY,GAAG,CAAC,CAAC,IAC1C,EAAE,EAAE,KAAK,QAAQ,EAAE,GACf;wBAAE,GAAG,CAAC;wBAAE,QAAQ;wBAAsB,iBAAiB,KAAK,MAAM;oBAAC,IACnE;gBAEN,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;YACxD,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,IAAI,QAAQ,+DAA+D;YACzE,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtD,YAAY;YAEZ,sBAAsB;YACtB,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,MAAM,cAAc,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,EAAE;YAClE,MAAM,qBAAqB,YAAY,MAAM,CAAC,CAAC,IAAe,EAAE,EAAE,KAAK;YACvE,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;QACxD;IACF;IAEA,MAAM,iBAAiB,CAAC,WAAmB;QACzC,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAA,IACnC,EAAE,EAAE,KAAK,YACL;gBAAE,GAAG,CAAC;gBAAE,aAAa;YAAQ,IAC7B;QAEN,YAAY;QAEZ,sBAAsB;QACtB,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,MAAM,cAAc,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,EAAE;QAClE,MAAM,qBAAqB,YAAY,GAAG,CAAC,CAAC,IAC1C,EAAE,EAAE,KAAK,YACL;gBAAE,GAAG,CAAC;gBAAE,aAAa;YAAQ,IAC7B;QAEN,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;IACxD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAIvD,6LAAC;4BACC,SAAS,IAAM,oBAAoB;4BACnC,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;YAOjC,kCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAIvE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO,YAAY,KAAK;wCACxB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC3E,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,OAAO,YAAY,QAAQ;wCAC3B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAgB,CAAC;wCAC7F,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,6LAAC;gDAAO,OAAM;0DAAK;;;;;;;;;;;;;;;;;;0CAIvB,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO,YAAY,aAAa;wCAChC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACnF,KAAK,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wCAC3C,WAAU;;;;;;;;;;;;0CAId,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,6LAAC;wCACC,MAAK;wCACL,OAAO,YAAY,aAAa;wCAChC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,eAAe,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCACnF,WAAU;;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,6LAAC;gCACC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;0BACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;yCAKlD,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4BAAqB,WAAU;sCAC9B,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAGhB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;;0EACd,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,QAAQ,WAAW,IAAI,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,WAAW,GAAG,uBAAuB;gEAAE,QAAQ,8IAAA,CAAA,KAAE;4DAAC;;;;;;;kEAEpG,6LAAC;kEAAM,QAAQ,QAAQ,KAAK,OAAO,WAAW;;;;;;kEAC9C,6LAAC;wDAAK,WAAW,CAAC,+BAA+B,EAC/C,QAAQ,MAAM,KAAK,cACf,yEACA,4EACJ;kEACC,QAAQ,MAAM,KAAK,cAAc,eAAe;;;;;;;;;;;;0DAIrD,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,WAAW,IAAI,IAAI,KAAK,QAAQ,WAAW,KAAK,IAAI,UAAU,QAAQ,MAAM,KAAK,6BACxF,6LAAC;oDAAK,WAAU;8DAAmD;;;;;;;;;;;;;;;;;kDAOzE,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,MAAM,KAAK,6BAClB;;kEACE,6LAAC;wDACC,SAAS,IAAM,WAAW;wDAC1B,WAAU;;0EAEV,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG9B,6LAAC;wDACC,SAAS;4DACP,MAAM,cAAc,OAAO,0CACzB,QAAQ,WAAW,GAAG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,WAAW,GAAG,sBAAsB;4DAEpF,IAAI,aAAa;gEACf,MAAM,UAAU,IAAI,KAAK;gEACzB,IAAI,UAAU,IAAI,QAAQ;oEACxB,eAAe,QAAQ,EAAE,EAAG;gEAC9B,OAAO;oEACL,MAAM;gEACR;4DACF;wDACF;wDACA,WAAU;;0EAEV,6LAAC,8MAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;;0DAKlC,6LAAC;gDACC,SAAS,IAAM,uBAAuB,QAAQ,EAAE;gDAChD,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;2BAlE5B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;AA8ElC;GAtWgB;KAAA", "debugId": null}}, {"offset": {"line": 1888, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Yeni%20klas%C3%B6r%20%283%29/article-generator/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ArticleGenerator } from '@/components/ArticleGenerator';\nimport { WordPressConfig } from '@/components/WordPressConfig';\nimport { ScheduledArticles } from '@/components/ScheduledArticles';\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState('generator');\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      <div className=\"container mx-auto px-4 py-8\">\n        <header className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-2\">\n            AI Makale Üreticisi\n          </h1>\n          <p className=\"text-lg text-gray-600 dark:text-gray-300\">\n            DeepSeek AI ile profesyonel makaleler oluşturun ve WordPress sitenizde yayınlayın\n          </p>\n        </header>\n\n        <div className=\"w-full\">\n          <div className=\"flex justify-center mb-8\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-lg p-1 shadow-lg\">\n              <button\n                onClick={() => setActiveTab('generator')}\n                className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                  activeTab === 'generator'\n                    ? 'bg-blue-500 text-white'\n                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n              >\n                Makale Üretici\n              </button>\n              <button\n                onClick={() => setActiveTab('wordpress')}\n                className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                  activeTab === 'wordpress'\n                    ? 'bg-blue-500 text-white'\n                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n              >\n                WordPress Ayarları\n              </button>\n              <button\n                onClick={() => setActiveTab('scheduled')}\n                className={`px-6 py-2 rounded-md font-medium transition-colors ${\n                  activeTab === 'scheduled'\n                    ? 'bg-blue-500 text-white'\n                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'\n                }`}\n              >\n                Planlı Makaleler\n              </button>\n            </div>\n          </div>\n\n          {activeTab === 'generator' && <ArticleGenerator />}\n          {activeTab === 'wordpress' && <WordPressConfig />}\n          {activeTab === 'scheduled' && <ScheduledArticles />}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAO,WAAU;;sCAChB,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;;;;;;;8BAK1D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,cACV,2BACA,6EACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,cACV,2BACA,6EACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,mDAAmD,EAC7D,cAAc,cACV,2BACA,6EACJ;kDACH;;;;;;;;;;;;;;;;;wBAMJ,cAAc,6BAAe,6LAAC,yIAAA,CAAA,mBAAgB;;;;;wBAC9C,cAAc,6BAAe,6LAAC,wIAAA,CAAA,kBAAe;;;;;wBAC7C,cAAc,6BAAe,6LAAC,0IAAA,CAAA,oBAAiB;;;;;;;;;;;;;;;;;;;;;;AAK1D;GA1DwB;KAAA", "debugId": null}}]}