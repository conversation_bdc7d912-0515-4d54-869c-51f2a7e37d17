[{"name": "hot-reloader", "duration": 154, "timestamp": 13959353319, "id": 3, "tags": {"version": "15.3.4"}, "startTime": 1750358457536, "traceId": "46c2f2332e157f7c"}, {"name": "setup-dev-bundler", "duration": 749809, "timestamp": 13959064527, "id": 2, "parentId": 1, "tags": {}, "startTime": 1750358457248, "traceId": "46c2f2332e157f7c"}, {"name": "run-instrumentation-hook", "duration": 27, "timestamp": 13959886437, "id": 4, "parentId": 1, "tags": {}, "startTime": 1750358458069, "traceId": "46c2f2332e157f7c"}, {"name": "start-dev-server", "duration": 1520693, "timestamp": 13958387619, "id": 1, "tags": {"cpus": "6", "platform": "win32", "memory.freeMem": "20028600320", "memory.totalMem": "34287902720", "memory.heapSizeLimit": "17193500672", "memory.rss": "181166080", "memory.heapTotal": "101912576", "memory.heapUsed": "68660592"}, "startTime": 1750358456571, "traceId": "46c2f2332e157f7c"}, {"name": "compile-path", "duration": 7069332, "timestamp": 13978203009, "id": 7, "tags": {"trigger": "/"}, "startTime": 1750358476386, "traceId": "46c2f2332e157f7c"}, {"name": "ensure-page", "duration": 7070712, "timestamp": 13978202469, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1750358476385, "traceId": "46c2f2332e157f7c"}]