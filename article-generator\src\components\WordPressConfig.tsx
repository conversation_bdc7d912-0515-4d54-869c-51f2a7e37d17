'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, XCircle, Loader2, Save, Send, Eye } from 'lucide-react';
import { WordPressConfig as WPConfig, Article } from '@/types';

export function WordPressConfig() {
  const [config, setConfig] = useState<WPConfig>({
    siteUrl: '',
    username: '',
    appPassword: ''
  });
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isTesting, setIsTesting] = useState(false);
  const [savedArticles, setSavedArticles] = useState<Article[]>([]);
  const [isPublishing, setIsPublishing] = useState<string | null>(null);

  useEffect(() => {
    // Load saved config from localStorage
    const savedConfig = localStorage.getItem('wordpress-config');
    if (savedConfig) {
      setConfig(JSON.parse(savedConfig));
    }

    // Load saved articles from localStorage
    const savedArticlesData = localStorage.getItem('saved-articles');
    if (savedArticlesData) {
      setSavedArticles(JSON.parse(savedArticlesData));
    }
  }, []);

  const saveConfig = () => {
    localStorage.setItem('wordpress-config', JSON.stringify(config));
    alert('WordPress ayarları kaydedildi!');
  };

  const testConnection = async () => {
    if (!config.siteUrl || !config.username || !config.appPassword) {
      alert('Lütfen tüm alanları doldurun');
      return;
    }

    setIsTesting(true);
    try {
      const response = await fetch('/api/wordpress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'test',
          config
        })
      });
      const data = await response.json();
      setIsConnected(data.success);
      
      if (data.success) {
        alert('WordPress bağlantısı başarılı!');
        saveConfig();
      } else {
        alert('WordPress bağlantısı başarısız. Lütfen bilgilerinizi kontrol edin.');
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      setIsConnected(false);
      alert('Bağlantı testi sırasında hata oluştu');
    } finally {
      setIsTesting(false);
    }
  };

  const publishToWordPress = async (article: Article) => {
    if (!isConnected) {
      alert('Önce WordPress bağlantısını test edin');
      return;
    }

    setIsPublishing(article.id || '');
    try {
      const response = await fetch('/api/wordpress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          config,
          article: {
            ...article,
            status: 'published'
          }
        })
      });
      const data = await response.json();
      
      if (data.success) {
        alert('Makale WordPress\'e başarıyla yayınlandı!');
        // Update article with WordPress post ID
        const updatedArticles = savedArticles.map(a => 
          a.id === article.id 
            ? { ...a, wordpressPostId: data.postId, status: 'published' as const }
            : a
        );
        setSavedArticles(updatedArticles);
        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));
      } else {
        alert('WordPress\'e yayınlama başarısız');
      }
    } catch (error) {
      console.error('Publishing failed:', error);
      alert('Yayınlama sırasında hata oluştu');
    } finally {
      setIsPublishing(null);
    }
  };

  const scheduleArticle = async (article: Article, scheduledDate: Date) => {
    if (!isConnected) {
      alert('Önce WordPress bağlantısını test edin');
      return;
    }

    try {
      const response = await fetch('/api/wordpress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          config,
          article: {
            ...article,
            status: 'scheduled',
            scheduledAt: scheduledDate
          }
        })
      });
      const data = await response.json();
      
      if (data.success) {
        alert('Makale planlandı!');
        const updatedArticles = savedArticles.map(a => 
          a.id === article.id 
            ? { ...a, wordpressPostId: data.postId, status: 'scheduled' as const, scheduledAt: scheduledDate }
            : a
        );
        setSavedArticles(updatedArticles);
        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));
      } else {
        alert('Makale planlama başarısız');
      }
    } catch (error) {
      console.error('Scheduling failed:', error);
      alert('Planlama sırasında hata oluştu');
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* WordPress Configuration */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          WordPress Bağlantı Ayarları
        </h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              WordPress Site URL
            </label>
            <input
              type="url"
              value={config.siteUrl}
              onChange={(e) => setConfig(prev => ({ ...prev, siteUrl: e.target.value }))}
              placeholder="https://yoursite.com"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Kullanıcı Adı
            </label>
            <input
              type="text"
              value={config.username}
              onChange={(e) => setConfig(prev => ({ ...prev, username: e.target.value }))}
              placeholder="WordPress kullanıcı adınız"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Uygulama Şifresi
            </label>
            <input
              type="password"
              value={config.appPassword}
              onChange={(e) => setConfig(prev => ({ ...prev, appPassword: e.target.value }))}
              placeholder="WordPress uygulama şifreniz"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              WordPress Admin → Kullanıcılar → Profil → Uygulama Şifreleri bölümünden oluşturabilirsiniz
            </p>
          </div>

          <div className="flex gap-3">
            <button
              onClick={saveConfig}
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              Kaydet
            </button>
            
            <button
              onClick={testConnection}
              disabled={isTesting}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 flex items-center gap-2"
            >
              {isTesting ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : isConnected === true ? (
                <CheckCircle className="w-4 h-4" />
              ) : isConnected === false ? (
                <XCircle className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
              Bağlantıyı Test Et
            </button>
          </div>

          {isConnected !== null && (
            <div className={`p-3 rounded-lg ${
              isConnected 
                ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300' 
                : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
            }`}>
              {isConnected 
                ? '✅ WordPress bağlantısı başarılı!' 
                : '❌ WordPress bağlantısı başarısız. Lütfen bilgilerinizi kontrol edin.'
              }
            </div>
          )}
        </div>
      </div>

      {/* Saved Articles */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          Kaydedilmiş Makaleler
        </h2>
        
        {savedArticles.length === 0 ? (
          <p className="text-gray-500 dark:text-gray-400 text-center py-8">
            Henüz kaydedilmiş makale bulunmuyor
          </p>
        ) : (
          <div className="space-y-4">
            {savedArticles.map((article) => (
              <div key={article.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                      {article.title}
                    </h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                      {article.language === 'tr' ? 'Türkçe' : 'English'} • 
                      {new Date(article.createdAt).toLocaleDateString('tr-TR')}
                    </p>
                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        article.status === 'published' 
                          ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                          : article.status === 'scheduled'
                          ? 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300'
                          : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                      }`}>
                        {article.status === 'published' ? 'Yayınlandı' : 
                         article.status === 'scheduled' ? 'Planlandı' : 'Taslak'}
                      </span>
                      {article.wordpressPostId && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          WP ID: {article.wordpressPostId}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {article.status === 'draft' && (
                      <button
                        onClick={() => publishToWordPress(article)}
                        disabled={isPublishing === article.id || !isConnected}
                        className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 flex items-center gap-1 text-sm"
                      >
                        {isPublishing === article.id ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <Send className="w-3 h-3" />
                        )}
                        Yayınla
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
