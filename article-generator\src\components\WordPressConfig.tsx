'use client';

import { useState, useEffect } from 'react';
import {
  CheckCircle,
  XCircle,
  Loader2,
  Save,
  Send,
  Eye,
  Settings,
  Globe,
  Key,
  User,
  AlertTriangle,
  ExternalLink,
  Clock,
  FileText
} from 'lucide-react';
import { WordPressConfig as WPConfig, Article } from '@/types';

export function WordPressConfig() {
  const [config, setConfig] = useState<WPConfig>({
    siteUrl: '',
    username: '',
    appPassword: ''
  });
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isTesting, setIsTesting] = useState(false);
  const [savedArticles, setSavedArticles] = useState<Article[]>([]);
  const [isPublishing, setIsPublishing] = useState<string | null>(null);

  useEffect(() => {
    // Load saved config from localStorage
    const savedConfig = localStorage.getItem('wordpress-config');
    if (savedConfig) {
      setConfig(JSON.parse(savedConfig));
    }

    // Load saved articles from localStorage
    const savedArticlesData = localStorage.getItem('saved-articles');
    if (savedArticlesData) {
      setSavedArticles(JSON.parse(savedArticlesData));
    }
  }, []);

  const saveConfig = () => {
    localStorage.setItem('wordpress-config', JSON.stringify(config));
    alert('WordPress ayarları kaydedildi!');
  };

  const testConnection = async () => {
    if (!config.siteUrl || !config.username || !config.appPassword) {
      alert('Lütfen tüm alanları doldurun');
      return;
    }

    setIsTesting(true);
    try {
      const response = await fetch('/api/wordpress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'test',
          config
        })
      });
      const data = await response.json();
      setIsConnected(data.success);
      
      if (data.success) {
        alert('WordPress bağlantısı başarılı!');
        saveConfig();
      } else {
        alert('WordPress bağlantısı başarısız. Lütfen bilgilerinizi kontrol edin.');
      }
    } catch (error) {
      console.error('Connection test failed:', error);
      setIsConnected(false);
      alert('Bağlantı testi sırasında hata oluştu');
    } finally {
      setIsTesting(false);
    }
  };

  const publishToWordPress = async (article: Article) => {
    if (!isConnected) {
      alert('Önce WordPress bağlantısını test edin');
      return;
    }

    setIsPublishing(article.id || '');
    try {
      const response = await fetch('/api/wordpress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          config,
          article: {
            ...article,
            status: 'published'
          }
        })
      });
      const data = await response.json();
      
      if (data.success) {
        alert('Makale WordPress\'e başarıyla yayınlandı!');
        // Update article with WordPress post ID
        const updatedArticles = savedArticles.map(a => 
          a.id === article.id 
            ? { ...a, wordpressPostId: data.postId, status: 'published' as const }
            : a
        );
        setSavedArticles(updatedArticles);
        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));
      } else {
        alert('WordPress\'e yayınlama başarısız');
      }
    } catch (error) {
      console.error('Publishing failed:', error);
      alert('Yayınlama sırasında hata oluştu');
    } finally {
      setIsPublishing(null);
    }
  };

  const scheduleArticle = async (article: Article, scheduledDate: Date) => {
    if (!isConnected) {
      alert('Önce WordPress bağlantısını test edin');
      return;
    }

    try {
      const response = await fetch('/api/wordpress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          config,
          article: {
            ...article,
            status: 'scheduled',
            scheduledAt: scheduledDate
          }
        })
      });
      const data = await response.json();
      
      if (data.success) {
        alert('Makale planlandı!');
        const updatedArticles = savedArticles.map(a => 
          a.id === article.id 
            ? { ...a, wordpressPostId: data.postId, status: 'scheduled' as const, scheduledAt: scheduledDate }
            : a
        );
        setSavedArticles(updatedArticles);
        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));
      } else {
        alert('Makale planlama başarısız');
      }
    } catch (error) {
      console.error('Scheduling failed:', error);
      alert('Planlama sırasında hata oluştu');
    }
  };

  return (
    <div className="max-w-5xl mx-auto space-y-8">
      {/* WordPress Configuration */}
      <div className="glass-card p-8 animate-fadeInUp">
        <div className="flex items-center gap-3 mb-8">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center">
            <Settings className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold text-slate-800 dark:text-slate-200">
              WordPress Bağlantı Ayarları
            </h2>
            <p className="text-slate-500 dark:text-slate-400">
              WordPress sitenize bağlanmak için gerekli bilgileri girin
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="input-floating">
              <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 z-10" />
              <input
                type="url"
                value={config.siteUrl}
                onChange={(e) => setConfig(prev => ({ ...prev, siteUrl: e.target.value }))}
                placeholder=" "
                className="input-modern pl-12"
                id="wp-url"
              />
              <label htmlFor="wp-url">WordPress Site URL</label>
            </div>

            <div className="input-floating">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 z-10" />
              <input
                type="text"
                value={config.username}
                onChange={(e) => setConfig(prev => ({ ...prev, username: e.target.value }))}
                placeholder=" "
                className="input-modern pl-12"
                id="wp-username"
              />
              <label htmlFor="wp-username">Kullanıcı Adı</label>
            </div>

            <div className="input-floating">
              <Key className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 z-10" />
              <input
                type="password"
                value={config.appPassword}
                onChange={(e) => setConfig(prev => ({ ...prev, appPassword: e.target.value }))}
                placeholder=" "
                className="input-modern pl-12"
                id="wp-password"
              />
              <label htmlFor="wp-password">Uygulama Şifresi</label>
            </div>

            <div className="alert-modern alert-info">
              <div className="flex items-start gap-3">
                <AlertTriangle className="w-5 h-5 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="font-medium">Uygulama Şifresi Nasıl Oluşturulur?</p>
                  <p className="text-sm mt-1">
                    WordPress Admin → Kullanıcılar → Profil → Uygulama Şifreleri bölümünden oluşturabilirsiniz
                  </p>
                  <a
                    href="https://wordpress.org/support/article/application-passwords/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-1 text-sm text-blue-600 dark:text-blue-400 hover:underline mt-2"
                  >
                    Detaylı Rehber <ExternalLink className="w-3 h-3" />
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            {/* Connection Status */}
            <div className="bg-slate-50 dark:bg-slate-800/50 rounded-xl p-6">
              <h3 className="font-medium text-slate-800 dark:text-slate-200 mb-4">
                Bağlantı Durumu
              </h3>
              {isConnected === null ? (
                <div className="flex items-center gap-3 text-slate-500 dark:text-slate-400">
                  <div className="w-3 h-3 bg-slate-300 dark:bg-slate-600 rounded-full"></div>
                  <span>Henüz test edilmedi</span>
                </div>
              ) : isConnected ? (
                <div className="flex items-center gap-3 text-emerald-600 dark:text-emerald-400">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-medium">Bağlantı başarılı!</span>
                </div>
              ) : (
                <div className="flex items-center gap-3 text-red-600 dark:text-red-400">
                  <XCircle className="w-5 h-5" />
                  <span className="font-medium">Bağlantı başarısız</span>
                </div>
              )}
            </div>

            {/* Quick Setup Guide */}
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl p-6">
              <h3 className="font-medium text-slate-800 dark:text-slate-200 mb-3">
                Hızlı Kurulum
              </h3>
              <div className="space-y-2 text-sm text-slate-600 dark:text-slate-400">
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>WordPress sitenizin URL'sini girin</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>Admin kullanıcı adınızı yazın</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>Uygulama şifresi oluşturun</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>Bağlantıyı test edin</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mt-8">
          <button
            onClick={saveConfig}
            className="btn-success flex items-center justify-center gap-2 flex-1"
          >
            <Save className="w-4 h-4" />
            <span>Ayarları Kaydet</span>
          </button>

          <button
            onClick={testConnection}
            disabled={isTesting || !config.siteUrl || !config.username || !config.appPassword}
            className="btn-primary flex items-center justify-center gap-2 flex-1"
          >
            {isTesting ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Test Ediliyor...</span>
              </>
            ) : (
              <>
                <Eye className="w-4 h-4" />
                <span>Bağlantıyı Test Et</span>
              </>
            )}
          </button>
        </div>
        </div>
      </div>

      {/* Saved Articles */}
      <div className="glass-card p-8 animate-scaleIn">
        <div className="flex items-center gap-3 mb-8">
          <div className="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center">
            <FileText className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold text-slate-800 dark:text-slate-200">
              Kaydedilmiş Makaleler
            </h2>
            <p className="text-slate-500 dark:text-slate-400">
              Oluşturduğunuz makaleleri yönetin ve WordPress'e yayınlayın
            </p>
          </div>
        </div>

        {savedArticles.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-16 h-16 bg-slate-100 dark:bg-slate-800 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <FileText className="w-8 h-8 text-slate-400" />
            </div>
            <p className="text-slate-500 dark:text-slate-400 text-lg mb-2">
              Henüz kaydedilmiş makale bulunmuyor
            </p>
            <p className="text-slate-400 dark:text-slate-500 text-sm">
              Makale oluşturduğunuzda burada görünecek
            </p>
          </div>
        ) : (
          <div className="grid gap-6">
            {savedArticles.map((article) => (
              <div key={article.id} className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border border-slate-200 dark:border-slate-700 rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-start gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center flex-shrink-0">
                        <FileText className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-slate-800 dark:text-slate-200 mb-2 text-lg">
                          {article.title}
                        </h3>
                        <div className="flex items-center gap-4 text-sm text-slate-500 dark:text-slate-400 mb-3">
                          <div className="flex items-center gap-1">
                            <Globe className="w-4 h-4" />
                            <span>{article.language === 'tr' ? 'Türkçe' : 'English'}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>{new Date(article.createdAt).toLocaleDateString('tr-TR')}</span>
                          </div>
                          {article.wordpressPostId && (
                            <div className="flex items-center gap-1">
                              <ExternalLink className="w-4 h-4" />
                              <span>WP ID: {article.wordpressPostId}</span>
                            </div>
                          )}
                        </div>
                        <div className="flex items-center gap-3">
                          <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                            article.status === 'published'
                              ? 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300'
                              : article.status === 'scheduled'
                              ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300'
                              : 'bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300'
                          }`}>
                            {article.status === 'published' ? '✅ Yayınlandı' :
                             article.status === 'scheduled' ? '⏰ Planlandı' : '📝 Taslak'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-2 ml-4">
                    {article.status === 'draft' && (
                      <button
                        onClick={() => publishToWordPress(article)}
                        disabled={isPublishing === article.id || !isConnected}
                        className="btn-primary px-4 py-2 flex items-center gap-2"
                      >
                        {isPublishing === article.id ? (
                          <>
                            <Loader2 className="w-4 h-4 animate-spin" />
                            <span className="hidden sm:inline">Yayınlanıyor...</span>
                          </>
                        ) : (
                          <>
                            <Send className="w-4 h-4" />
                            <span className="hidden sm:inline">Yayınla</span>
                          </>
                        )}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
