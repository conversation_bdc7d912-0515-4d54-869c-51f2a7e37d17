'use client';

import { useState, useEffect } from 'react';
import { Wand2, Loader2, Copy, Download, Calendar, Image as ImageIcon } from 'lucide-react';
import { PexelsImage } from '@/types';

export function ArticleGenerator() {
  const [title, setTitle] = useState('');
  const [language, setLanguage] = useState<'tr' | 'en'>('tr');
  const [generatedArticle, setGeneratedArticle] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingTitle, setIsGeneratingTitle] = useState(false);
  const [selectedImages, setSelectedImages] = useState<PexelsImage[]>([]);
  const [showImageSearch, setShowImageSearch] = useState(false);
  const [imageSearchQuery, setImageSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<PexelsImage[]>([]);
  const [isSearchingImages, setIsSearchingImages] = useState(false);
  const [generationProgress, setGenerationProgress] = useState('');
  const [generationStartTime, setGenerationStartTime] = useState<number | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isGenerating && generationStartTime) {
      interval = setInterval(() => {
        setElapsedTime(Math.floor((Date.now() - generationStartTime) / 1000));
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isGenerating, generationStartTime]);

  const generateTitle = async () => {
    setIsGeneratingTitle(true);
    try {
      const response = await fetch('/api/generate-title', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ language })
      });
      const data = await response.json();
      setTitle(data.title);
    } catch (error) {
      console.error('Title generation failed:', error);
    } finally {
      setIsGeneratingTitle(false);
    }
  };

  const generateArticle = async () => {
    if (!title.trim()) {
      alert('Lütfen bir başlık girin');
      return;
    }

    setIsGenerating(true);
    setGeneratedArticle(''); // Clear previous content
    setGenerationProgress('Makale oluşturma isteği gönderiliyor...');
    setGenerationStartTime(Date.now());
    setElapsedTime(0);

    try {
      console.log('Starting article generation for:', title);

      setGenerationProgress('AI modeli ile bağlantı kuruluyor...');

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes timeout

      const response = await fetch('/api/generate-article', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ title: title.trim(), language }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      console.log('Response status:', response.status);
      setGenerationProgress('Yanıt alındı, işleniyor...');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('Response data:', data);
      setGenerationProgress('Makale içeriği hazırlanıyor...');

      if (data.success) {
        setGeneratedArticle(data.content);
        // Auto-search for images based on title
        searchImages(title);

        // Save article to localStorage
        const article = {
          id: Date.now().toString(),
          title: title.trim(),
          content: data.content,
          language,
          createdAt: new Date(),
          status: 'draft' as const
        };

        const savedArticles = localStorage.getItem('saved-articles');
        const existingArticles = savedArticles ? JSON.parse(savedArticles) : [];
        const updatedArticles = [...existingArticles, article];
        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));

        alert('Makale başarıyla oluşturuldu!');
      } else {
        const errorMessage = data.error || 'Bilinmeyen hata oluştu';
        console.error('Article generation failed:', errorMessage);
        alert('Makale oluşturulurken hata oluştu: ' + errorMessage);
      }
    } catch (error: any) {
      console.error('Article generation failed:', error);
      let errorMessage = 'Makale oluşturulurken hata oluştu';

      if (error.message.includes('timeout')) {
        errorMessage = 'İstek zaman aşımına uğradı. Lütfen tekrar deneyin.';
      } else if (error.message.includes('network')) {
        errorMessage = 'Ağ bağlantısı hatası. İnternet bağlantınızı kontrol edin.';
      } else if (error.message.includes('401')) {
        errorMessage = 'API anahtarı geçersiz. Lütfen ayarları kontrol edin.';
      } else if (error.message.includes('402')) {
        errorMessage = 'Yetersiz kredi. OpenRouter hesabınıza kredi ekleyin.';
      } else if (error.message.includes('429')) {
        errorMessage = 'Çok fazla istek. Lütfen biraz bekleyip tekrar deneyin.';
      }

      alert(errorMessage);
    } finally {
      setIsGenerating(false);
      setGenerationProgress('');
      setGenerationStartTime(null);
    }
  };

  const searchImages = async (query: string) => {
    setIsSearchingImages(true);
    try {
      const response = await fetch(`/api/search-images?query=${encodeURIComponent(query)}&perPage=12`);
      const data = await response.json();
      setSearchResults(data.images || []);
      setShowImageSearch(true);
    } catch (error) {
      console.error('Image search failed:', error);
    } finally {
      setIsSearchingImages(false);
    }
  };

  const toggleImageSelection = (image: PexelsImage) => {
    setSelectedImages(prev => {
      const isSelected = prev.some(img => img.id === image.id);
      if (isSelected) {
        return prev.filter(img => img.id !== image.id);
      } else {
        return [...prev, image];
      }
    });
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedArticle);
    alert('Makale panoya kopyalandı!');
  };

  const downloadAsHTML = () => {
    const blob = new Blob([generatedArticle], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${title.replace(/[^a-zA-Z0-9]/g, '_')}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Title Input Section */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Makale Başlığı
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Makale başlığını girin..."
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                />
                <button
                  onClick={generateTitle}
                  disabled={isGeneratingTitle}
                  className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50 flex items-center gap-2"
                >
                  {isGeneratingTitle ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Wand2 className="w-4 h-4" />
                  )}
                  AI Başlık
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Dil
              </label>
              <select
                value={language}
                onChange={(e) => setLanguage(e.target.value as 'tr' | 'en')}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="tr">Türkçe</option>
                <option value="en">English</option>
              </select>
            </div>

            {!isGenerating && (
              <div className="mb-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  ⚠️ Makale oluşturma işlemi 3-5 dakika sürebilir. Lütfen sayfayı kapatmayın.
                </p>
              </div>
            )}

            <button
              onClick={generateArticle}
              disabled={isGenerating || !title.trim()}
              className="w-full px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 flex items-center justify-center gap-2 font-medium"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="w-5 h-5 animate-spin" />
                  <div className="flex flex-col items-center">
                    <span>{generationProgress || 'Makale Oluşturuluyor...'}</span>
                    <span className="text-sm opacity-75">
                      {Math.floor(elapsedTime / 60)}:{(elapsedTime % 60).toString().padStart(2, '0')}
                    </span>
                  </div>
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5" />
                  Makale Oluştur
                </>
              )}
            </button>
          </div>

          {/* Image Selection Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Görseller ({selectedImages.length} seçili)
              </label>
              <button
                onClick={() => setShowImageSearch(!showImageSearch)}
                className="px-3 py-1 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center gap-1"
              >
                <ImageIcon className="w-4 h-4" />
                Görsel Ara
              </button>
            </div>

            {showImageSearch && (
              <div className="space-y-3">
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={imageSearchQuery}
                    onChange={(e) => setImageSearchQuery(e.target.value)}
                    placeholder="Görsel arama..."
                    className="flex-1 px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                  <button
                    onClick={() => searchImages(imageSearchQuery)}
                    disabled={isSearchingImages}
                    className="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
                  >
                    {isSearchingImages ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      'Ara'
                    )}
                  </button>
                </div>

                <div className="grid grid-cols-3 gap-2 max-h-64 overflow-y-auto">
                  {searchResults.map((image) => (
                    <div
                      key={image.id}
                      className={`relative cursor-pointer rounded-md overflow-hidden border-2 ${
                        selectedImages.some(img => img.id === image.id)
                          ? 'border-blue-500'
                          : 'border-transparent'
                      }`}
                      onClick={() => toggleImageSelection(image)}
                    >
                      <img
                        src={image.src.small}
                        alt={`Photo by ${image.photographer}`}
                        className="w-full h-20 object-cover"
                      />
                      {selectedImages.some(img => img.id === image.id) && (
                        <div className="absolute inset-0 bg-blue-500 bg-opacity-30 flex items-center justify-center">
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs">✓</span>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {selectedImages.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm text-gray-600 dark:text-gray-400">Seçili Görseller:</p>
                <div className="grid grid-cols-4 gap-2">
                  {selectedImages.map((image) => (
                    <div key={image.id} className="relative">
                      <img
                        src={image.src.small}
                        alt={`Photo by ${image.photographer}`}
                        className="w-full h-16 object-cover rounded-md"
                      />
                      <button
                        onClick={() => toggleImageSelection(image)}
                        className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs hover:bg-red-600"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Generated Article Display */}
      {generatedArticle && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Oluşturulan Makale
            </h2>
            <div className="flex gap-2">
              <button
                onClick={copyToClipboard}
                className="px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 flex items-center gap-2"
              >
                <Copy className="w-4 h-4" />
                Kopyala
              </button>
              <button
                onClick={downloadAsHTML}
                className="px-3 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center gap-2"
              >
                <Download className="w-4 h-4" />
                İndir
              </button>
            </div>
          </div>
          
          <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
            <div 
              className="prose dark:prose-invert max-w-none"
              dangerouslySetInnerHTML={{ __html: generatedArticle }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
