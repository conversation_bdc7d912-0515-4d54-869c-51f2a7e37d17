import axios from 'axios';
import { WordPressConfig, WordPressPost, Article } from '@/types';

export class WordPressService {
  private config: WordPressConfig;

  constructor(config: WordPressConfig) {
    this.config = config;
  }

  private getAuthHeader(): string {
    const credentials = Buffer.from(`${this.config.username}:${this.config.appPassword}`).toString('base64');
    return `Basic ${credentials}`;
  }

  private getApiUrl(): string {
    return `${this.config.siteUrl.replace(/\/$/, '')}/wp-json/wp/v2`;
  }

  async testConnection(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.getApiUrl()}/users/me`, {
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        }
      });
      return response.status === 200;
    } catch (error) {
      console.error('WordPress connection test failed:', error);
      return false;
    }
  }

  async createPost(article: Article): Promise<number | null> {
    try {
      const postData: Partial<WordPressPost> = {
        title: {
          raw: article.title
        },
        content: {
          raw: article.content
        },
        status: article.status === 'published' ? 'publish' : 
                article.status === 'scheduled' ? 'future' : 'draft'
      };

      if (article.scheduledAt && article.status === 'scheduled') {
        postData.date = article.scheduledAt.toISOString();
      }

      const response = await axios.post(`${this.getApiUrl()}/posts`, postData, {
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        }
      });

      return response.data.id;
    } catch (error) {
      console.error('WordPress post creation failed:', error);
      return null;
    }
  }

  async updatePost(postId: number, article: Article): Promise<boolean> {
    try {
      const postData: Partial<WordPressPost> = {
        title: {
          raw: article.title
        },
        content: {
          raw: article.content
        },
        status: article.status === 'published' ? 'publish' : 
                article.status === 'scheduled' ? 'future' : 'draft'
      };

      if (article.scheduledAt && article.status === 'scheduled') {
        postData.date = article.scheduledAt.toISOString();
      }

      const response = await axios.post(`${this.getApiUrl()}/posts/${postId}`, postData, {
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        }
      });

      return response.status === 200;
    } catch (error) {
      console.error('WordPress post update failed:', error);
      return false;
    }
  }

  async deletePost(postId: number): Promise<boolean> {
    try {
      const response = await axios.delete(`${this.getApiUrl()}/posts/${postId}`, {
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        }
      });

      return response.status === 200;
    } catch (error) {
      console.error('WordPress post deletion failed:', error);
      return false;
    }
  }

  async getPosts(page: number = 1, perPage: number = 10): Promise<WordPressPost[]> {
    try {
      const response = await axios.get(`${this.getApiUrl()}/posts`, {
        params: {
          page,
          per_page: perPage,
          _embed: true
        },
        headers: {
          'Authorization': this.getAuthHeader(),
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error) {
      console.error('WordPress posts fetch failed:', error);
      return [];
    }
  }
}
