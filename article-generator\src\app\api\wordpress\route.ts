import { NextRequest, NextResponse } from 'next/server';
import { WordPressService } from '@/lib/wordpress';
import { Article, WordPressConfig } from '@/types';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, config, article, postId } = body;

    if (!config || !config.siteUrl || !config.username || !config.appPassword) {
      return NextResponse.json(
        { success: false, error: 'WordPress configuration is required' },
        { status: 400 }
      );
    }

    const wordpressService = new WordPressService(config as WordPressConfig);

    switch (action) {
      case 'test':
        const isConnected = await wordpressService.testConnection();
        return NextResponse.json({ success: isConnected });

      case 'create':
        if (!article) {
          return NextResponse.json(
            { success: false, error: 'Article data is required' },
            { status: 400 }
          );
        }
        const createdPostId = await wordpressService.createPost(article as Article);
        return NextResponse.json({ 
          success: createdPostId !== null, 
          postId: createdPostId 
        });

      case 'update':
        if (!article || !postId) {
          return NextResponse.json(
            { success: false, error: 'Article data and post ID are required' },
            { status: 400 }
          );
        }
        const updated = await wordpressService.updatePost(postId, article as Article);
        return NextResponse.json({ success: updated });

      case 'delete':
        if (!postId) {
          return NextResponse.json(
            { success: false, error: 'Post ID is required' },
            { status: 400 }
          );
        }
        const deleted = await wordpressService.deletePost(postId);
        return NextResponse.json({ success: deleted });

      case 'list':
        const posts = await wordpressService.getPosts();
        return NextResponse.json({ success: true, posts });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('WordPress API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
