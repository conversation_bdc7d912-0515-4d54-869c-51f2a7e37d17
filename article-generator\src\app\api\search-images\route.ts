import { NextRequest, NextResponse } from 'next/server';
import { PexelsService } from '@/lib/pexels';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const perPage = parseInt(searchParams.get('perPage') || '15');

    if (!query) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      );
    }

    const pexelsService = new PexelsService();
    const images = await pexelsService.searchImages(query, perPage);

    return NextResponse.json({ images });
  } catch (error) {
    console.error('Image search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
