import { NextRequest, NextResponse } from 'next/server';
import { OpenRouterService } from '@/lib/openrouter';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const language = body.language || 'tr';

    const openRouterService = new OpenRouterService();
    const result = await openRouterService.generateTitle(language);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Title generation error:', error);
    return NextResponse.json(
      { title: language === 'tr' ? 'Yeni Makale Başlığı' : 'New Article Title', language },
      { status: 500 }
    );
  }
}
