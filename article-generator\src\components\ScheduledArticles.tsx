'use client';

import { useState, useEffect } from 'react';
import {
  Calendar,
  Clock,
  Edit,
  Trash2,
  Send,
  Plus,
  AlertTriangle,
  CheckCircle,
  FileText,
  Globe,
  Sparkles,
  Timer
} from 'lucide-react';
import { Article } from '@/types';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

export function ScheduledArticles() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [newSchedule, setNewSchedule] = useState({
    title: '',
    language: 'tr' as 'tr' | 'en',
    scheduledDate: '',
    scheduledTime: ''
  });

  useEffect(() => {
    // Load saved articles from localStorage
    const savedArticles = localStorage.getItem('saved-articles');
    if (savedArticles) {
      const parsedArticles = JSON.parse(savedArticles);
      setArticles(parsedArticles.filter((article: Article) => 
        article.status === 'scheduled' || article.scheduledAt
      ));
    }
  }, []);

  const scheduleNewArticle = async () => {
    if (!newSchedule.title || !newSchedule.scheduledDate || !newSchedule.scheduledTime) {
      alert('Lütfen tüm alanları doldurun');
      return;
    }

    const scheduledDateTime = new Date(`${newSchedule.scheduledDate}T${newSchedule.scheduledTime}`);
    
    if (scheduledDateTime <= new Date()) {
      alert('Planlanan tarih gelecekte olmalıdır');
      return;
    }

    // Generate article content
    try {
      const response = await fetch('/api/generate-article', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          title: newSchedule.title, 
          language: newSchedule.language 
        })
      });
      const data = await response.json();
      
      if (data.success) {
        const newArticle: Article = {
          id: Date.now().toString(),
          title: newSchedule.title,
          content: data.content,
          language: newSchedule.language,
          createdAt: new Date(),
          scheduledAt: scheduledDateTime,
          status: 'scheduled'
        };

        // Save to localStorage
        const savedArticles = localStorage.getItem('saved-articles');
        const existingArticles = savedArticles ? JSON.parse(savedArticles) : [];
        const updatedArticles = [...existingArticles, newArticle];
        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));
        
        setArticles(prev => [...prev, newArticle]);
        setShowScheduleForm(false);
        setNewSchedule({
          title: '',
          language: 'tr',
          scheduledDate: '',
          scheduledTime: ''
        });
        
        alert('Makale başarıyla planlandı!');
      } else {
        alert('Makale oluşturulurken hata oluştu: ' + data.error);
      }
    } catch (error) {
      console.error('Article scheduling failed:', error);
      alert('Makale planlama sırasında hata oluştu');
    }
  };

  const publishNow = async (article: Article) => {
    // Get WordPress config
    const wpConfig = localStorage.getItem('wordpress-config');
    if (!wpConfig) {
      alert('Önce WordPress ayarlarını yapılandırın');
      return;
    }

    try {
      const response = await fetch('/api/wordpress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          config: JSON.parse(wpConfig),
          article: {
            ...article,
            status: 'published'
          }
        })
      });
      const data = await response.json();
      
      if (data.success) {
        alert('Makale başarıyla yayınlandı!');
        
        // Update article status
        const updatedArticles = articles.map(a => 
          a.id === article.id 
            ? { ...a, status: 'published' as const, wordpressPostId: data.postId }
            : a
        );
        setArticles(updatedArticles);
        
        // Update localStorage
        const savedArticles = localStorage.getItem('saved-articles');
        const allArticles = savedArticles ? JSON.parse(savedArticles) : [];
        const updatedAllArticles = allArticles.map((a: Article) => 
          a.id === article.id 
            ? { ...a, status: 'published' as const, wordpressPostId: data.postId }
            : a
        );
        localStorage.setItem('saved-articles', JSON.stringify(updatedAllArticles));
      } else {
        alert('Yayınlama başarısız');
      }
    } catch (error) {
      console.error('Publishing failed:', error);
      alert('Yayınlama sırasında hata oluştu');
    }
  };

  const deleteScheduledArticle = (articleId: string) => {
    if (confirm('Bu planlanmış makaleyi silmek istediğinizden emin misiniz?')) {
      const updatedArticles = articles.filter(a => a.id !== articleId);
      setArticles(updatedArticles);
      
      // Update localStorage
      const savedArticles = localStorage.getItem('saved-articles');
      const allArticles = savedArticles ? JSON.parse(savedArticles) : [];
      const updatedAllArticles = allArticles.filter((a: Article) => a.id !== articleId);
      localStorage.setItem('saved-articles', JSON.stringify(updatedAllArticles));
    }
  };

  const updateSchedule = (articleId: string, newDate: Date) => {
    const updatedArticles = articles.map(a => 
      a.id === articleId 
        ? { ...a, scheduledAt: newDate }
        : a
    );
    setArticles(updatedArticles);
    
    // Update localStorage
    const savedArticles = localStorage.getItem('saved-articles');
    const allArticles = savedArticles ? JSON.parse(savedArticles) : [];
    const updatedAllArticles = allArticles.map((a: Article) => 
      a.id === articleId 
        ? { ...a, scheduledAt: newDate }
        : a
    );
    localStorage.setItem('saved-articles', JSON.stringify(updatedAllArticles));
  };

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="glass-card p-8 animate-fadeInUp">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl flex items-center justify-center">
              <Calendar className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-semibold text-slate-800 dark:text-slate-200">
                Planlı Makaleler
              </h2>
              <p className="text-slate-500 dark:text-slate-400 mt-1">
                Gelecekte yayınlanacak makalelerinizi yönetin ve takip edin
              </p>
            </div>
          </div>
          <button
            onClick={() => setShowScheduleForm(true)}
            className="btn-primary flex items-center gap-2 px-6 py-3"
          >
            <Plus className="w-5 h-5" />
            <span>Yeni Planlı Makale</span>
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mt-8">
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                <Calendar className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                  {articles.filter(a => a.status === 'scheduled').length}
                </div>
                <div className="text-sm text-blue-600 dark:text-blue-400">Planlı</div>
              </div>
            </div>
          </div>
          <div className="bg-emerald-50 dark:bg-emerald-900/20 rounded-xl p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-emerald-500 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
                  {articles.filter(a => a.status === 'published').length}
                </div>
                <div className="text-sm text-emerald-600 dark:text-emerald-400">Yayınlandı</div>
              </div>
            </div>
          </div>
          <div className="bg-amber-50 dark:bg-amber-900/20 rounded-xl p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-amber-500 rounded-lg flex items-center justify-center">
                <AlertTriangle className="w-5 h-5 text-white" />
              </div>
              <div>
                <div className="text-2xl font-bold text-amber-600 dark:text-amber-400">
                  {articles.filter(a => a.scheduledAt && new Date(a.scheduledAt) <= new Date() && a.status !== 'published').length}
                </div>
                <div className="text-sm text-amber-600 dark:text-amber-400">Gecikmiş</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Schedule Form */}
      {showScheduleForm && (
        <div className="glass-card p-8 animate-scaleIn">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-200">
              Yeni Makale Planla
            </h3>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="lg:col-span-2">
              <div className="input-floating">
                <input
                  type="text"
                  value={newSchedule.title}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, title: e.target.value }))}
                  placeholder=" "
                  className="input-modern"
                  id="schedule-title"
                />
                <label htmlFor="schedule-title">Makale Başlığı</label>
              </div>
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-slate-700 dark:text-slate-300">
                Dil Seçimi
              </label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setNewSchedule(prev => ({ ...prev, language: 'tr' }))}
                  className={`p-3 rounded-xl border-2 transition-all duration-200 ${
                    newSchedule.language === 'tr'
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <span className="text-lg">🇹🇷</span>
                    <span className="font-medium">Türkçe</span>
                  </div>
                </button>
                <button
                  onClick={() => setNewSchedule(prev => ({ ...prev, language: 'en' }))}
                  className={`p-3 rounded-xl border-2 transition-all duration-200 ${
                    newSchedule.language === 'en'
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <span className="text-lg">🇺🇸</span>
                    <span className="font-medium">English</span>
                  </div>
                </button>
              </div>
            </div>

            <div className="space-y-4">
              <div className="input-floating">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 z-10" />
                <input
                  type="date"
                  value={newSchedule.scheduledDate}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, scheduledDate: e.target.value }))}
                  min={new Date().toISOString().split('T')[0]}
                  className="input-modern pl-12"
                  id="schedule-date"
                />
                <label htmlFor="schedule-date">Yayın Tarihi</label>
              </div>

              <div className="input-floating">
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400 z-10" />
                <input
                  type="time"
                  value={newSchedule.scheduledTime}
                  onChange={(e) => setNewSchedule(prev => ({ ...prev, scheduledTime: e.target.value }))}
                  className="input-modern pl-12"
                  id="schedule-time"
                />
                <label htmlFor="schedule-time">Yayın Saati</label>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 mt-8">
            <button
              onClick={scheduleNewArticle}
              disabled={!newSchedule.title || !newSchedule.scheduledDate || !newSchedule.scheduledTime}
              className="btn-primary flex items-center justify-center gap-2 flex-1"
            >
              <Timer className="w-4 h-4" />
              <span>Makaleyi Planla</span>
            </button>
            <button
              onClick={() => setShowScheduleForm(false)}
              className="btn-ghost flex items-center justify-center gap-2 flex-1"
            >
              <span>İptal</span>
            </button>
          </div>
        </div>
      )}

      {/* Scheduled Articles List */}
      <div className="glass-card p-8 animate-slideInRight">
        {articles.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-slate-100 dark:bg-slate-800 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Calendar className="w-10 h-10 text-slate-400" />
            </div>
            <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">
              Henüz planlanmış makale bulunmuyor
            </h3>
            <p className="text-slate-500 dark:text-slate-400 mb-6">
              İlk planlı makalenizi oluşturmak için yukarıdaki butonu kullanın
            </p>
            <button
              onClick={() => setShowScheduleForm(true)}
              className="btn-primary flex items-center gap-2 mx-auto"
            >
              <Plus className="w-4 h-4" />
              <span>İlk Makaleyi Planla</span>
            </button>
          </div>
        ) : (
          <div className="space-y-6">
            {articles.map((article) => (
              <div key={article.id} className="bg-white/50 dark:bg-slate-800/50 backdrop-blur-sm border border-slate-200 dark:border-slate-700 rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-start gap-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0 ${
                        article.status === 'published'
                          ? 'bg-gradient-to-br from-emerald-500 to-green-600'
                          : article.scheduledAt && new Date(article.scheduledAt) <= new Date()
                          ? 'bg-gradient-to-br from-amber-500 to-orange-600'
                          : 'bg-gradient-to-br from-blue-500 to-purple-600'
                      }`}>
                        {article.status === 'published' ? (
                          <CheckCircle className="w-5 h-5 text-white" />
                        ) : article.scheduledAt && new Date(article.scheduledAt) <= new Date() ? (
                          <AlertTriangle className="w-5 h-5 text-white" />
                        ) : (
                          <Clock className="w-5 h-5 text-white" />
                        )}
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-slate-800 dark:text-slate-200 mb-2 text-lg">
                          {article.title}
                        </h3>

                        <div className="flex flex-wrap items-center gap-4 text-sm text-slate-500 dark:text-slate-400 mb-3">
                          <div className="flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            <span>
                              {article.scheduledAt && format(new Date(article.scheduledAt), 'dd MMMM yyyy, HH:mm', { locale: tr })}
                            </span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Globe className="w-4 h-4" />
                            <span>{article.language === 'tr' ? 'Türkçe' : 'English'}</span>
                          </div>
                        </div>

                        <div className="flex items-center gap-3 mb-3">
                          <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                            article.status === 'published'
                              ? 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300'
                              : 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                          }`}>
                            {article.status === 'published' ? '✅ Yayınlandı' : '⏰ Planlandı'}
                          </span>
                        </div>

                        {article.scheduledAt && new Date(article.scheduledAt) <= new Date() && article.status !== 'published' && (
                          <div className="alert-modern alert-warning">
                            <div className="flex items-center gap-2">
                              <AlertTriangle className="w-4 h-4" />
                              <span className="text-sm font-medium">
                                Planlanan zaman geçti - Manuel yayınlama gerekli
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-2 ml-4">
                    {article.status !== 'published' && (
                      <>
                        <button
                          onClick={() => publishNow(article)}
                          className="btn-success px-4 py-2 flex items-center gap-2"
                        >
                          <Send className="w-4 h-4" />
                          <span className="hidden sm:inline">Şimdi Yayınla</span>
                        </button>
                        <button
                          onClick={() => {
                            const newDateTime = prompt('Yeni tarih ve saat (YYYY-MM-DD HH:MM):',
                              article.scheduledAt ? format(new Date(article.scheduledAt), 'yyyy-MM-dd HH:mm') : ''
                            );
                            if (newDateTime) {
                              const newDate = new Date(newDateTime);
                              if (newDate > new Date()) {
                                updateSchedule(article.id!, newDate);
                              } else {
                                alert('Yeni tarih gelecekte olmalıdır');
                              }
                            }
                          }}
                          className="btn-ghost px-4 py-2 flex items-center gap-2"
                        >
                          <Edit className="w-4 h-4" />
                          <span className="hidden sm:inline">Düzenle</span>
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => deleteScheduledArticle(article.id!)}
                      className="btn-warning px-4 py-2 flex items-center gap-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span className="hidden sm:inline">Sil</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
