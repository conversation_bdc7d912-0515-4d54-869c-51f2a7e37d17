'use client';

import { useState, useEffect } from 'react';
import { Calendar, Clock, Edit, Trash2, Send, Plus } from 'lucide-react';
import { Article } from '@/types';
import { format } from 'date-fns';
import { tr } from 'date-fns/locale';

export function ScheduledArticles() {
  const [articles, setArticles] = useState<Article[]>([]);
  const [showScheduleForm, setShowScheduleForm] = useState(false);
  const [newSchedule, setNewSchedule] = useState({
    title: '',
    language: 'tr' as 'tr' | 'en',
    scheduledDate: '',
    scheduledTime: ''
  });

  useEffect(() => {
    // Load saved articles from localStorage
    const savedArticles = localStorage.getItem('saved-articles');
    if (savedArticles) {
      const parsedArticles = JSON.parse(savedArticles);
      setArticles(parsedArticles.filter((article: Article) => 
        article.status === 'scheduled' || article.scheduledAt
      ));
    }
  }, []);

  const scheduleNewArticle = async () => {
    if (!newSchedule.title || !newSchedule.scheduledDate || !newSchedule.scheduledTime) {
      alert('Lütfen tüm alanları doldurun');
      return;
    }

    const scheduledDateTime = new Date(`${newSchedule.scheduledDate}T${newSchedule.scheduledTime}`);
    
    if (scheduledDateTime <= new Date()) {
      alert('Planlanan tarih gelecekte olmalıdır');
      return;
    }

    // Generate article content
    try {
      const response = await fetch('/api/generate-article', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          title: newSchedule.title, 
          language: newSchedule.language 
        })
      });
      const data = await response.json();
      
      if (data.success) {
        const newArticle: Article = {
          id: Date.now().toString(),
          title: newSchedule.title,
          content: data.content,
          language: newSchedule.language,
          createdAt: new Date(),
          scheduledAt: scheduledDateTime,
          status: 'scheduled'
        };

        // Save to localStorage
        const savedArticles = localStorage.getItem('saved-articles');
        const existingArticles = savedArticles ? JSON.parse(savedArticles) : [];
        const updatedArticles = [...existingArticles, newArticle];
        localStorage.setItem('saved-articles', JSON.stringify(updatedArticles));
        
        setArticles(prev => [...prev, newArticle]);
        setShowScheduleForm(false);
        setNewSchedule({
          title: '',
          language: 'tr',
          scheduledDate: '',
          scheduledTime: ''
        });
        
        alert('Makale başarıyla planlandı!');
      } else {
        alert('Makale oluşturulurken hata oluştu: ' + data.error);
      }
    } catch (error) {
      console.error('Article scheduling failed:', error);
      alert('Makale planlama sırasında hata oluştu');
    }
  };

  const publishNow = async (article: Article) => {
    // Get WordPress config
    const wpConfig = localStorage.getItem('wordpress-config');
    if (!wpConfig) {
      alert('Önce WordPress ayarlarını yapılandırın');
      return;
    }

    try {
      const response = await fetch('/api/wordpress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          config: JSON.parse(wpConfig),
          article: {
            ...article,
            status: 'published'
          }
        })
      });
      const data = await response.json();
      
      if (data.success) {
        alert('Makale başarıyla yayınlandı!');
        
        // Update article status
        const updatedArticles = articles.map(a => 
          a.id === article.id 
            ? { ...a, status: 'published' as const, wordpressPostId: data.postId }
            : a
        );
        setArticles(updatedArticles);
        
        // Update localStorage
        const savedArticles = localStorage.getItem('saved-articles');
        const allArticles = savedArticles ? JSON.parse(savedArticles) : [];
        const updatedAllArticles = allArticles.map((a: Article) => 
          a.id === article.id 
            ? { ...a, status: 'published' as const, wordpressPostId: data.postId }
            : a
        );
        localStorage.setItem('saved-articles', JSON.stringify(updatedAllArticles));
      } else {
        alert('Yayınlama başarısız');
      }
    } catch (error) {
      console.error('Publishing failed:', error);
      alert('Yayınlama sırasında hata oluştu');
    }
  };

  const deleteScheduledArticle = (articleId: string) => {
    if (confirm('Bu planlanmış makaleyi silmek istediğinizden emin misiniz?')) {
      const updatedArticles = articles.filter(a => a.id !== articleId);
      setArticles(updatedArticles);
      
      // Update localStorage
      const savedArticles = localStorage.getItem('saved-articles');
      const allArticles = savedArticles ? JSON.parse(savedArticles) : [];
      const updatedAllArticles = allArticles.filter((a: Article) => a.id !== articleId);
      localStorage.setItem('saved-articles', JSON.stringify(updatedAllArticles));
    }
  };

  const updateSchedule = (articleId: string, newDate: Date) => {
    const updatedArticles = articles.map(a => 
      a.id === articleId 
        ? { ...a, scheduledAt: newDate }
        : a
    );
    setArticles(updatedArticles);
    
    // Update localStorage
    const savedArticles = localStorage.getItem('saved-articles');
    const allArticles = savedArticles ? JSON.parse(savedArticles) : [];
    const updatedAllArticles = allArticles.map((a: Article) => 
      a.id === articleId 
        ? { ...a, scheduledAt: newDate }
        : a
    );
    localStorage.setItem('saved-articles', JSON.stringify(updatedAllArticles));
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Planlı Makaleler
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Gelecekte yayınlanacak makalelerinizi yönetin
            </p>
          </div>
          <button
            onClick={() => setShowScheduleForm(true)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Yeni Planlı Makale
          </button>
        </div>
      </div>

      {/* Schedule Form */}
      {showScheduleForm && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Yeni Makale Planla
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Makale Başlığı
              </label>
              <input
                type="text"
                value={newSchedule.title}
                onChange={(e) => setNewSchedule(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Makale başlığını girin..."
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Dil
              </label>
              <select
                value={newSchedule.language}
                onChange={(e) => setNewSchedule(prev => ({ ...prev, language: e.target.value as 'tr' | 'en' }))}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="tr">Türkçe</option>
                <option value="en">English</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Tarih
              </label>
              <input
                type="date"
                value={newSchedule.scheduledDate}
                onChange={(e) => setNewSchedule(prev => ({ ...prev, scheduledDate: e.target.value }))}
                min={new Date().toISOString().split('T')[0]}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Saat
              </label>
              <input
                type="time"
                value={newSchedule.scheduledTime}
                onChange={(e) => setNewSchedule(prev => ({ ...prev, scheduledTime: e.target.value }))}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>
          
          <div className="flex gap-3 mt-6">
            <button
              onClick={scheduleNewArticle}
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 flex items-center gap-2"
            >
              <Calendar className="w-4 h-4" />
              Planla
            </button>
            <button
              onClick={() => setShowScheduleForm(false)}
              className="px-4 py-2 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-400 dark:hover:bg-gray-500"
            >
              İptal
            </button>
          </div>
        </div>
      )}

      {/* Scheduled Articles List */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        {articles.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              Henüz planlanmış makale bulunmuyor
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {articles.map((article) => (
              <div key={article.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                      {article.title}
                    </h3>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
                      <span className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {article.scheduledAt && format(new Date(article.scheduledAt), 'dd MMMM yyyy, HH:mm', { locale: tr })}
                      </span>
                      <span>{article.language === 'tr' ? 'Türkçe' : 'English'}</span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        article.status === 'published' 
                          ? 'bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                          : 'bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300'
                      }`}>
                        {article.status === 'published' ? 'Yayınlandı' : 'Planlandı'}
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {article.scheduledAt && new Date(article.scheduledAt) <= new Date() && article.status !== 'published' && (
                        <span className="text-orange-600 dark:text-orange-400 font-medium">
                          ⚠️ Planlanan zaman geçti - Manuel yayınlama gerekli
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex gap-2">
                    {article.status !== 'published' && (
                      <>
                        <button
                          onClick={() => publishNow(article)}
                          className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center gap-1 text-sm"
                        >
                          <Send className="w-3 h-3" />
                          Şimdi Yayınla
                        </button>
                        <button
                          onClick={() => {
                            const newDateTime = prompt('Yeni tarih ve saat (YYYY-MM-DD HH:MM):', 
                              article.scheduledAt ? format(new Date(article.scheduledAt), 'yyyy-MM-dd HH:mm') : ''
                            );
                            if (newDateTime) {
                              const newDate = new Date(newDateTime);
                              if (newDate > new Date()) {
                                updateSchedule(article.id!, newDate);
                              } else {
                                alert('Yeni tarih gelecekte olmalıdır');
                              }
                            }
                          }}
                          className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center gap-1 text-sm"
                        >
                          <Edit className="w-3 h-3" />
                          Düzenle
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => deleteScheduledArticle(article.id!)}
                      className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 flex items-center gap-1 text-sm"
                    >
                      <Trash2 className="w-3 h-3" />
                      Sil
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
