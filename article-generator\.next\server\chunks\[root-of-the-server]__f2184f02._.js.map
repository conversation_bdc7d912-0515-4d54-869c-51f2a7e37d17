{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Yeni%20klas%C3%B6r%20%283%29/article-generator/src/lib/pexels.ts"], "sourcesContent": ["import axios from 'axios';\nimport { PexelsResponse, PexelsImage } from '@/types';\n\nconst PEXELS_API_URL = 'https://api.pexels.com/v1';\n\nexport class PexelsService {\n  private apiKey: string;\n\n  constructor() {\n    this.apiKey = process.env.PEXELS_API_KEY || '';\n  }\n\n  async searchImages(query: string, perPage: number = 15): Promise<PexelsImage[]> {\n    if (!this.apiKey) {\n      console.warn('Pexels API key not configured');\n      return [];\n    }\n\n    try {\n      const response = await axios.get<PexelsResponse>(`${PEXELS_API_URL}/search`, {\n        params: {\n          query,\n          per_page: perPage,\n          page: 1\n        },\n        headers: {\n          'Authorization': this.apiKey\n        }\n      });\n\n      return response.data.photos;\n    } catch (error) {\n      console.error('Pexels API Error:', error);\n      return [];\n    }\n  }\n\n  async getCuratedImages(perPage: number = 15): Promise<PexelsImage[]> {\n    if (!this.apiKey) {\n      console.warn('Pexels API key not configured');\n      return [];\n    }\n\n    try {\n      const response = await axios.get<PexelsResponse>(`${PEXELS_API_URL}/curated`, {\n        params: {\n          per_page: perPage,\n          page: 1\n        },\n        headers: {\n          'Authorization': this.apiKey\n        }\n      });\n\n      return response.data.photos;\n    } catch (error) {\n      console.error('Pexels API Error:', error);\n      return [];\n    }\n  }\n\n  extractKeywordsFromTitle(title: string): string {\n    // Simple keyword extraction - remove common words and get meaningful terms\n    const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'];\n    const turkishCommonWords = ['ve', 'ile', 'için', 'olan', 'olarak', 'bu', 'şu', 'o', 'bir', 'de', 'da', 'den', 'dan', 'deki', 'daki', 'nin', 'nın', 'nun', 'nün'];\n    \n    const allCommonWords = [...commonWords, ...turkishCommonWords];\n    \n    const words = title.toLowerCase()\n      .replace(/[^\\w\\s]/g, '')\n      .split(/\\s+/)\n      .filter(word => word.length > 2 && !allCommonWords.includes(word));\n    \n    return words.slice(0, 3).join(' '); // Take first 3 meaningful words\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,MAAM,iBAAiB;AAEhB,MAAM;IACH,OAAe;IAEvB,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,cAAc,IAAI;IAC9C;IAEA,MAAM,aAAa,KAAa,EAAE,UAAkB,EAAE,EAA0B;QAC9E,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO,EAAE;QACX;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAiB,GAAG,eAAe,OAAO,CAAC,EAAE;gBAC3E,QAAQ;oBACN;oBACA,UAAU;oBACV,MAAM;gBACR;gBACA,SAAS;oBACP,iBAAiB,IAAI,CAAC,MAAM;gBAC9B;YACF;YAEA,OAAO,SAAS,IAAI,CAAC,MAAM;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,EAAE;QACX;IACF;IAEA,MAAM,iBAAiB,UAAkB,EAAE,EAA0B;QACnE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,QAAQ,IAAI,CAAC;YACb,OAAO,EAAE;QACX;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAiB,GAAG,eAAe,QAAQ,CAAC,EAAE;gBAC5E,QAAQ;oBACN,UAAU;oBACV,MAAM;gBACR;gBACA,SAAS;oBACP,iBAAiB,IAAI,CAAC,MAAM;gBAC9B;YACF;YAEA,OAAO,SAAS,IAAI,CAAC,MAAM;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,EAAE;QACX;IACF;IAEA,yBAAyB,KAAa,EAAU;QAC9C,2EAA2E;QAC3E,MAAM,cAAc;YAAC;YAAO;YAAK;YAAM;YAAO;YAAM;YAAO;YAAM;YAAM;YAAM;YAAM;YAAO;YAAM;YAAQ;YAAM;YAAM;YAAO;YAAO;YAAQ;YAAM;YAAQ;YAAS;YAAQ;YAAO;YAAO;YAAM;YAAQ;YAAO;YAAQ;YAAS;YAAS;SAAS;QAC/O,MAAM,qBAAqB;YAAC;YAAM;YAAO;YAAQ;YAAQ;YAAU;YAAM;YAAM;YAAK;YAAO;YAAM;YAAM;YAAO;YAAO;YAAQ;YAAQ;YAAO;YAAO;YAAO;SAAM;QAEhK,MAAM,iBAAiB;eAAI;eAAgB;SAAmB;QAE9D,MAAM,QAAQ,MAAM,WAAW,GAC5B,OAAO,CAAC,YAAY,IACpB,KAAK,CAAC,OACN,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,KAAK,CAAC,eAAe,QAAQ,CAAC;QAE9D,OAAO,MAAM,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,gCAAgC;IACtE;AACF", "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Yeni%20klas%C3%B6r%20%283%29/article-generator/src/app/api/search-images/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { PexelsService } from '@/lib/pexels';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const query = searchParams.get('query');\n    const perPage = parseInt(searchParams.get('perPage') || '15');\n\n    if (!query) {\n      return NextResponse.json(\n        { error: 'Query parameter is required' },\n        { status: 400 }\n      );\n    }\n\n    const pexelsService = new PexelsService();\n    const images = await pexelsService.searchImages(query, perPage);\n\n    return NextResponse.json({ images });\n  } catch (error) {\n    console.error('Image search error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,UAAU,SAAS,aAAa,GAAG,CAAC,cAAc;QAExD,IAAI,CAAC,OAAO;YACV,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,gBAAgB,IAAI,sHAAA,CAAA,gBAAa;QACvC,MAAM,SAAS,MAAM,cAAc,YAAY,CAAC,OAAO;QAEvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAO;IACpC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}