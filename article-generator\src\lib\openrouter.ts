import axios from 'axios';
import { ArticleGenerationRequest, ArticleGenerationResponse, GeneratedTitle } from '@/types';

const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

export class OpenRouterService {
  private apiKey: string;
  private model: string;

  constructor() {
    this.apiKey = process.env.OPENROUTER_API_KEY || '';
    this.model = process.env.OPENROUTER_MODEL || 'deepseek/deepseek-chat-v3-0324:free';
  }

  private async makeRequest(prompt: string): Promise<string> {
    if (!this.apiKey) {
      throw new Error('OpenRouter API key is not configured');
    }

    try {
      console.log('Making request to OpenRouter API...');
      console.log('Model:', this.model);
      console.log('Prompt length:', prompt.length);

      const response = await axios.post(
        OPENROUTER_API_URL,
        {
          model: this.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 8000, // Increased for longer articles
          temperature: 0.7,
          stream: false
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
            'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
            'X-Title': process.env.NEXT_PUBLIC_APP_NAME || 'Article Generator'
          },
          timeout: 120000 // 2 minutes timeout
        }
      );

      console.log('OpenRouter API response status:', response.status);

      if (!response.data || !response.data.choices || response.data.choices.length === 0) {
        console.error('Invalid response structure:', response.data);
        throw new Error('Invalid response from OpenRouter API');
      }

      const content = response.data.choices[0]?.message?.content;
      if (!content) {
        console.error('No content in response:', response.data.choices[0]);
        throw new Error('No content received from OpenRouter API');
      }

      console.log('Successfully received content, length:', content.length);
      return content;
    } catch (error: any) {
      console.error('OpenRouter API Error:', error);

      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);

        if (error.response.status === 401) {
          throw new Error('Invalid API key. Please check your OpenRouter API key.');
        } else if (error.response.status === 402) {
          throw new Error('Insufficient credits. Please add more credits to your OpenRouter account.');
        } else if (error.response.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        } else if (error.response.status === 503) {
          throw new Error('Model is currently unavailable. Please try again later.');
        } else {
          throw new Error(`OpenRouter API error: ${error.response.data?.error?.message || error.message}`);
        }
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('Request timeout. The article generation is taking too long. Please try again.');
      } else {
        throw new Error(`Network error: ${error.message}`);
      }
    }
  }

  async generateArticle({ title, language }: ArticleGenerationRequest): Promise<ArticleGenerationResponse> {
    const languageMap = {
      'tr': 'Turkish',
      'en': 'English'
    };

    const prompt = `Write a comprehensive article about "${title}" in ${languageMap[language]}.

Requirements:
- Minimum 2000 words
- Use HTML formatting
- Include 4-5 main sections with <h2> tags
- Include subsections with <h3> tags where appropriate
- Use <p> tags for paragraphs (150-250 words each)
- Bold important keywords with <b> tags
- Include at least 2 relevant HTML tables with <table> tags
- Include at least 1 unordered list with <ul> and <li> tags
- End with 2 FAQ sections using <h4> tags (200+ words each)

Structure:
1. Introduction paragraph
2. Main content sections (4-5 sections)
3. Relevant tables and lists
4. FAQ section with 2 common questions

Write in a professional, informative style. Make sure all content is relevant to "${title}".`;

    try {
      const content = await this.makeRequest(prompt);
      return {
        content,
        success: true
      };
    } catch (error) {
      return {
        content: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async generateTitle(language: 'tr' | 'en' = 'tr'): Promise<GeneratedTitle> {
    const languageMap = {
      'tr': 'Turkish',
      'en': 'English'
    };

    const prompt = `Generate a compelling, SEO-friendly article title in ${languageMap[language]} language. The title should be about a trending topic, informative, and engaging. Return only the title, nothing else.`;

    try {
      const title = await this.makeRequest(prompt);
      return {
        title: title.trim().replace(/^["']|["']$/g, ''), // Remove quotes if present
        language
      };
    } catch (error) {
      console.error('Title generation error:', error);
      return {
        title: language === 'tr' ? 'Yeni Makale Başlığı' : 'New Article Title',
        language
      };
    }
  }
}
