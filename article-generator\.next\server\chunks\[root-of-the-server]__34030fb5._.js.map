{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Yeni%20klas%C3%B6r%20%283%29/article-generator/src/lib/openrouter.ts"], "sourcesContent": ["import axios from 'axios';\nimport { ArticleGenerationRequest, ArticleGenerationResponse, GeneratedTitle } from '@/types';\n\nconst OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';\n\nexport class OpenRouterService {\n  private apiKey: string;\n  private model: string;\n\n  constructor() {\n    this.apiKey = process.env.OPENROUTER_API_KEY || '';\n    this.model = process.env.OPENROUTER_MODEL || 'deepseek/deepseek-chat-v3-0324:free';\n  }\n\n  private async makeRequest(prompt: string): Promise<string> {\n    if (!this.apiKey) {\n      throw new Error('OpenRouter API key is not configured');\n    }\n\n    try {\n      console.log('Making request to OpenRouter API...');\n      console.log('Model:', this.model);\n      console.log('Prompt length:', prompt.length);\n\n      const response = await axios.post(\n        OPENROUTER_API_URL,\n        {\n          model: this.model,\n          messages: [\n            {\n              role: 'user',\n              content: prompt\n            }\n          ],\n          max_tokens: 8000, // Increased for longer articles\n          temperature: 0.7,\n          stream: false\n        },\n        {\n          headers: {\n            'Authorization': `Bearer ${this.apiKey}`,\n            'Content-Type': 'application/json',\n            'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\n            'X-Title': process.env.NEXT_PUBLIC_APP_NAME || 'Article Generator'\n          },\n          timeout: 120000 // 2 minutes timeout\n        }\n      );\n\n      console.log('OpenRouter API response status:', response.status);\n\n      if (!response.data || !response.data.choices || response.data.choices.length === 0) {\n        console.error('Invalid response structure:', response.data);\n        throw new Error('Invalid response from OpenRouter API');\n      }\n\n      const content = response.data.choices[0]?.message?.content;\n      if (!content) {\n        console.error('No content in response:', response.data.choices[0]);\n        throw new Error('No content received from OpenRouter API');\n      }\n\n      console.log('Successfully received content, length:', content.length);\n      return content;\n    } catch (error: any) {\n      console.error('OpenRouter API Error:', error);\n\n      if (error.response) {\n        console.error('Response status:', error.response.status);\n        console.error('Response data:', error.response.data);\n\n        if (error.response.status === 401) {\n          throw new Error('Invalid API key. Please check your OpenRouter API key.');\n        } else if (error.response.status === 402) {\n          throw new Error('Insufficient credits. Please add more credits to your OpenRouter account.');\n        } else if (error.response.status === 429) {\n          throw new Error('Rate limit exceeded. Please try again later.');\n        } else if (error.response.status === 503) {\n          throw new Error('Model is currently unavailable. Please try again later.');\n        } else {\n          throw new Error(`OpenRouter API error: ${error.response.data?.error?.message || error.message}`);\n        }\n      } else if (error.code === 'ECONNABORTED') {\n        throw new Error('Request timeout. The article generation is taking too long. Please try again.');\n      } else {\n        throw new Error(`Network error: ${error.message}`);\n      }\n    }\n  }\n\n  async generateArticle({ title, language }: ArticleGenerationRequest): Promise<ArticleGenerationResponse> {\n    const languageMap = {\n      'tr': 'Turkish',\n      'en': 'English'\n    };\n\n    const prompt = `Write a comprehensive article about \"${title}\" in ${languageMap[language]}.\n\nRequirements:\n- Minimum 2000 words\n- Use HTML formatting\n- Include 4-5 main sections with <h2> tags\n- Include subsections with <h3> tags where appropriate\n- Use <p> tags for paragraphs (150-250 words each)\n- Bold important keywords with <b> tags\n- Include at least 2 relevant HTML tables with <table> tags\n- Include at least 1 unordered list with <ul> and <li> tags\n- End with 2 FAQ sections using <h4> tags (200+ words each)\n\nStructure:\n1. Introduction paragraph\n2. Main content sections (4-5 sections)\n3. Relevant tables and lists\n4. FAQ section with 2 common questions\n\nWrite in a professional, informative style. Make sure all content is relevant to \"${title}\".`;\n\n    try {\n      const content = await this.makeRequest(prompt);\n      return {\n        content,\n        success: true\n      };\n    } catch (error) {\n      return {\n        content: '',\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error occurred'\n      };\n    }\n  }\n\n  async generateTitle(language: 'tr' | 'en' = 'tr'): Promise<GeneratedTitle> {\n    const languageMap = {\n      'tr': 'Turkish',\n      'en': 'English'\n    };\n\n    const prompt = `Generate a compelling, SEO-friendly article title in ${languageMap[language]} language. The title should be about a trending topic, informative, and engaging. Return only the title, nothing else.`;\n\n    try {\n      const title = await this.makeRequest(prompt);\n      return {\n        title: title.trim().replace(/^[\"']|[\"']$/g, ''), // Remove quotes if present\n        language\n      };\n    } catch (error) {\n      console.error('Title generation error:', error);\n      return {\n        title: language === 'tr' ? 'Yeni Makale Başlığı' : 'New Article Title',\n        language\n      };\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGA,MAAM,qBAAqB;AAEpB,MAAM;IACH,OAAe;IACf,MAAc;IAEtB,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,kBAAkB,IAAI;QAChD,IAAI,CAAC,KAAK,GAAG,QAAQ,GAAG,CAAC,gBAAgB,IAAI;IAC/C;IAEA,MAAc,YAAY,MAAc,EAAmB;QACzD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,UAAU,IAAI,CAAC,KAAK;YAChC,QAAQ,GAAG,CAAC,kBAAkB,OAAO,MAAM;YAE3C,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CAC/B,oBACA;gBACE,OAAO,IAAI,CAAC,KAAK;gBACjB,UAAU;oBACR;wBACE,MAAM;wBACN,SAAS;oBACX;iBACD;gBACD,YAAY;gBACZ,aAAa;gBACb,QAAQ;YACV,GACA;gBACE,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE;oBACxC,gBAAgB;oBAChB,gBAAgB,QAAQ,GAAG,CAAC,mBAAmB,IAAI;oBACnD,WAAW,yDAAoC;gBACjD;gBACA,SAAS,OAAO,oBAAoB;YACtC;YAGF,QAAQ,GAAG,CAAC,mCAAmC,SAAS,MAAM;YAE9D,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,IAAI,SAAS,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;gBAClF,QAAQ,KAAK,CAAC,+BAA+B,SAAS,IAAI;gBAC1D,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAU,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,SAAS;YACnD,IAAI,CAAC,SAAS;gBACZ,QAAQ,KAAK,CAAC,2BAA2B,SAAS,IAAI,CAAC,OAAO,CAAC,EAAE;gBACjE,MAAM,IAAI,MAAM;YAClB;YAEA,QAAQ,GAAG,CAAC,0CAA0C,QAAQ,MAAM;YACpE,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,yBAAyB;YAEvC,IAAI,MAAM,QAAQ,EAAE;gBAClB,QAAQ,KAAK,CAAC,oBAAoB,MAAM,QAAQ,CAAC,MAAM;gBACvD,QAAQ,KAAK,CAAC,kBAAkB,MAAM,QAAQ,CAAC,IAAI;gBAEnD,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;oBACjC,MAAM,IAAI,MAAM;gBAClB,OAAO,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;oBACxC,MAAM,IAAI,MAAM;gBAClB,OAAO,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;oBACxC,MAAM,IAAI,MAAM;gBAClB,OAAO,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;oBACxC,MAAM,IAAI,MAAM;gBAClB,OAAO;oBACL,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE,OAAO,WAAW,MAAM,OAAO,EAAE;gBACjG;YACF,OAAO,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACxC,MAAM,IAAI,MAAM;YAClB,OAAO;gBACL,MAAM,IAAI,MAAM,CAAC,eAAe,EAAE,MAAM,OAAO,EAAE;YACnD;QACF;IACF;IAEA,MAAM,gBAAgB,EAAE,KAAK,EAAE,QAAQ,EAA4B,EAAsC;QACvG,MAAM,cAAc;YAClB,MAAM;YACN,MAAM;QACR;QAEA,MAAM,SAAS,CAAC,qCAAqC,EAAE,MAAM,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC;;;;;;;;;;;;;;;;;;;kFAmBZ,EAAE,MAAM,EAAE,CAAC;QAEzF,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,CAAC,WAAW,CAAC;YACvC,OAAO;gBACL;gBACA,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,MAAM,cAAc,WAAwB,IAAI,EAA2B;QACzE,MAAM,cAAc;YAClB,MAAM;YACN,MAAM;QACR;QAEA,MAAM,SAAS,CAAC,qDAAqD,EAAE,WAAW,CAAC,SAAS,CAAC,sHAAsH,CAAC;QAEpN,IAAI;YACF,MAAM,QAAQ,MAAM,IAAI,CAAC,WAAW,CAAC;YACrC,OAAO;gBACL,OAAO,MAAM,IAAI,GAAG,OAAO,CAAC,gBAAgB;gBAC5C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;gBACL,OAAO,aAAa,OAAO,wBAAwB;gBACnD;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Yeni%20klas%C3%B6r%20%283%29/article-generator/src/app/api/generate-article/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { OpenRouterService } from '@/lib/openrouter';\nimport { ArticleGenerationRequest } from '@/types';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body: ArticleGenerationRequest = await request.json();\n    \n    if (!body.title || !body.language) {\n      return NextResponse.json(\n        { success: false, error: 'Title and language are required' },\n        { status: 400 }\n      );\n    }\n\n    const openRouterService = new OpenRouterService();\n    const result = await openRouterService.generateArticle(body);\n\n    return NextResponse.json(result);\n  } catch (error) {\n    console.error('Article generation error:', error);\n    return NextResponse.json(\n      { success: false, error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAiC,MAAM,QAAQ,IAAI;QAEzD,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,QAAQ,EAAE;YACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkC,GAC3D;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,oBAAoB,IAAI,0HAAA,CAAA,oBAAiB;QAC/C,MAAM,SAAS,MAAM,kBAAkB,eAAe,CAAC;QAEvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAwB,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}