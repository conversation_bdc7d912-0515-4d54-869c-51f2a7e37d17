import axios from 'axios';
import { PexelsResponse, PexelsImage } from '@/types';

const PEXELS_API_URL = 'https://api.pexels.com/v1';

export class PexelsService {
  private apiKey: string;

  constructor() {
    this.apiKey = process.env.PEXELS_API_KEY || '';
  }

  async searchImages(query: string, perPage: number = 15): Promise<PexelsImage[]> {
    if (!this.apiKey) {
      console.warn('Pexels API key not configured');
      return [];
    }

    try {
      const response = await axios.get<PexelsResponse>(`${PEXELS_API_URL}/search`, {
        params: {
          query,
          per_page: perPage,
          page: 1
        },
        headers: {
          'Authorization': this.apiKey
        }
      });

      return response.data.photos;
    } catch (error) {
      console.error('Pexels API Error:', error);
      return [];
    }
  }

  async getCuratedImages(perPage: number = 15): Promise<PexelsImage[]> {
    if (!this.apiKey) {
      console.warn('Pexels API key not configured');
      return [];
    }

    try {
      const response = await axios.get<PexelsResponse>(`${PEXELS_API_URL}/curated`, {
        params: {
          per_page: perPage,
          page: 1
        },
        headers: {
          'Authorization': this.apiKey
        }
      });

      return response.data.photos;
    } catch (error) {
      console.error('Pexels API Error:', error);
      return [];
    }
  }

  extractKeywordsFromTitle(title: string): string {
    // Simple keyword extraction - remove common words and get meaningful terms
    const commonWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'];
    const turkishCommonWords = ['ve', 'ile', 'için', 'olan', 'olarak', 'bu', 'şu', 'o', 'bir', 'de', 'da', 'den', 'dan', 'deki', 'daki', 'nin', 'nın', 'nun', 'nün'];
    
    const allCommonWords = [...commonWords, ...turkishCommonWords];
    
    const words = title.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2 && !allCommonWords.includes(word));
    
    return words.slice(0, 3).join(' '); // Take first 3 meaningful words
  }
}
