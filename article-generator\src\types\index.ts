export interface Article {
  id?: string;
  title: string;
  content: string;
  language: 'tr' | 'en';
  createdAt: Date;
  scheduledAt?: Date;
  status: 'draft' | 'scheduled' | 'published';
  images?: PexelsImage[];
  wordpressPostId?: number;
}

export interface PexelsImage {
  id: number;
  url: string;
  photographer: string;
  photographer_url: string;
  src: {
    original: string;
    large2x: string;
    large: string;
    medium: string;
    small: string;
    portrait: string;
    landscape: string;
    tiny: string;
  };
}

export interface PexelsResponse {
  photos: PexelsImage[];
  total_results: number;
  page: number;
  per_page: number;
  next_page?: string;
}

export interface WordPressConfig {
  siteUrl: string;
  username: string;
  appPassword: string;
}

export interface WordPressPost {
  id?: number;
  title: {
    rendered?: string;
    raw?: string;
  };
  content: {
    rendered?: string;
    raw?: string;
  };
  status: 'publish' | 'draft' | 'future';
  date?: string;
  featured_media?: number;
}

export interface GeneratedTitle {
  title: string;
  language: 'tr' | 'en';
}

export interface ArticleGenerationRequest {
  title: string;
  language: 'tr' | 'en';
}

export interface ArticleGenerationResponse {
  content: string;
  success: boolean;
  error?: string;
}
